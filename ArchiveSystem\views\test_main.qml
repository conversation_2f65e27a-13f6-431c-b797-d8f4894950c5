import QtQuick 2.15
import QtQuick.Window 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

ApplicationWindow {
    id: mainWindow
    visible: true
    width: 1280
    height: 800
    title: "Banking Archive System - Test"
    
    Rectangle {
        anchors.fill: parent
        color: "#f0f0f0"
        
        Column {
            anchors.centerIn: parent
            spacing: 20
            
            Text {
                text: "🏦 نظام الأرشفة المصرفي"
                font.pixelSize: 32
                font.bold: true
                color: "#2c3e50"
                anchors.horizontalCenter: parent.horizontalCenter
            }
            
            Text {
                text: "تم تشغيل التطبيق بنجاح!"
                font.pixelSize: 18
                color: "#27ae60"
                anchors.horizontalCenter: parent.horizontalCenter
            }
            
            Button {
                text: "اختبار الاتصال"
                anchors.horizontalCenter: parent.horizontalCenter
                onClicked: {
                    statusText.text = "تم النقر على الزر بنجاح!"
                    statusText.color = "#27ae60"
                }
            }
            
            Text {
                id: statusText
                text: "جا<PERSON><PERSON> للاختبار..."
                font.pixelSize: 14
                color: "#7f8c8d"
                anchors.horizontalCenter: parent.horizontalCenter
            }
        }
    }
}
