import sys
import os
import logging
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtQml import QQmlApplicationEngine
from PySide6.QtCore import QTranslator, QLocale, QDir, QObject, Signal, Slot, Property
from PySide6.QtGui import QIcon

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import configuration
from config import (
    APP_NAME, APP_LANGUAGE, LOG_PATH, DEBUG, 
    ROOT_DIR, RTL_LANGUAGES, UI_SCALE_FACTOR
)

# Import ViewModels
from viewmodels.user_viewmodel import UserViewModel
from viewmodels.document_viewmodel import DocumentViewModel
from viewmodels.category_viewmodel import CategoryViewModel
from viewmodels.scanner_viewmodel import ScannerViewModel

# Configure logger
logger = logging.getLogger('main')
logger.setLevel(logging.DEBUG if DEBUG else logging.INFO)
log_file = LOG_PATH / 'application.log'
file_handler = logging.FileHandler(log_file)
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(file_handler)

# Add console handler if in debug mode
if DEBUG:
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(console_handler)


class AppController(QObject):
    """Main application controller"""
    
    # Signals
    languageChanged = Signal(str)
    themeChanged = Signal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._language = APP_LANGUAGE
        self._theme = 'light'
        self._is_rtl = self._language in RTL_LANGUAGES
    
    @Property(str, notify=languageChanged)
    def language(self):
        return self._language
    
    @language.setter
    def language(self, language):
        if self._language != language:
            self._language = language
            self._is_rtl = language in RTL_LANGUAGES
            self.languageChanged.emit(language)
    
    @Property(str, notify=themeChanged)
    def theme(self):
        return self._theme
    
    @theme.setter
    def theme(self, theme):
        if self._theme != theme:
            self._theme = theme
            self.themeChanged.emit(theme)
    
    @Property(bool, notify=languageChanged)
    def isRtl(self):
        return self._is_rtl
    
    @Slot(result=str)
    def getAppName(self):
        return APP_NAME
    
    @Slot(result=bool)
    def isDebugMode(self):
        return DEBUG
    
    @Slot(str, result=str)
    def getAbsolutePath(self, relative_path):
        """Convert a relative path to an absolute path"""
        return str(ROOT_DIR / relative_path)


def main():
    # Create application
    app = QApplication(sys.argv)
    app.setApplicationName(APP_NAME)
    app.setOrganizationName("Banking Systems")
    app.setOrganizationDomain("banking-systems.com")
    
    # Set application icon
    icon_path = ROOT_DIR / "assets" / "icons" / "app_icon.png"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    # Set up translation
    translator = QTranslator()
    translation_path = ROOT_DIR / "assets" / "translations"
    if translator.load(QLocale(APP_LANGUAGE), "app", "_", str(translation_path)):
        app.installTranslator(translator)
    
    # Create QML engine
    engine = QQmlApplicationEngine()
    
    # Register ViewModels
    app_controller = AppController()
    user_viewmodel = UserViewModel()
    document_viewmodel = DocumentViewModel()
    category_viewmodel = CategoryViewModel()
    scanner_viewmodel = ScannerViewModel()
    
    # Set context properties
    engine.rootContext().setContextProperty("appController", app_controller)
    engine.rootContext().setContextProperty("userViewModel", user_viewmodel)
    engine.rootContext().setContextProperty("documentViewModel", document_viewmodel)
    engine.rootContext().setContextProperty("categoryViewModel", category_viewmodel)
    engine.rootContext().setContextProperty("scannerViewModel", scanner_viewmodel)
    
    # Set additional context properties
    engine.rootContext().setContextProperty("uiScale", UI_SCALE_FACTOR)
    
    # Load main QML file (using test version for now)
    qml_file = ROOT_DIR / "views" / "test_main.qml"
    engine.load(str(qml_file))
    
    # Check if QML loaded successfully
    if not engine.rootObjects():
        logger.error("Failed to load QML")
        return -1
    
    logger.info(f"Application started: {APP_NAME}")
    
    # Run application
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())