import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: permissionsView
    color: "#f8f9fa"
    
    property var permissions: []
    property var rolePermissions: {}
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20
        
        // Header
        RowLayout {
            Layout.fillWidth: true
            
            Text {
                text: "🔐 إدارة الصلاحيات"
                font.pixelSize: 24
                font.bold: true
                color: "#2c3e50"
            }
            
            Item {
                Layout.fillWidth: true
            }
            
            Button {
                text: "رجوع"
                
                background: Rectangle {
                    color: "#95a5a6"
                    radius: 5
                }
                
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                
                onClicked: mainWindow.showUsersView()
            }
        }
        
        // Role tabs
        RowLayout {
            Layout.fillWidth: true
            spacing: 10
            
            Repeater {
                model: ["admin", "manager", "archivist", "viewer"]
                
                Button {
                    property bool isSelected: permissionsView.selectedRole === modelData
                    property string role: modelData
                    
                    text: getRoleDisplayName(modelData)
                    
                    background: Rectangle {
                        color: isSelected ? "#3498db" : "#ecf0f1"
                        radius: 5
                        border.color: "#bdc3c7"
                        border.width: 1
                    }
                    
                    contentItem: Text {
                        text: parent.text
                        color: parent.isSelected ? "white" : "#2c3e50"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                    
                    onClicked: {
                        permissionsView.selectedRole = role
                        loadRolePermissions(role)
                    }
                }
            }
        }
        
        // Permissions matrix
        ScrollView {
            Layout.fillWidth: true
            Layout.fillHeight: true
            
            ColumnLayout {
                width: parent.width
                spacing: 15
                
                Text {
                    text: "صلاحيات الدور: " + getRoleDisplayName(permissionsView.selectedRole || "admin")
                    font.pixelSize: 18
                    font.bold: true
                    color: "#2c3e50"
                }
                
                // Permissions groups
                Repeater {
                    model: getPermissionGroups()
                    
                    Rectangle {
                        Layout.fillWidth: true
                        height: groupColumn.height + 40
                        color: "white"
                        radius: 10
                        border.color: "#e0e0e0"
                        border.width: 1
                        
                        ColumnLayout {
                            id: groupColumn
                            anchors.fill: parent
                            anchors.margins: 20
                            spacing: 15
                            
                            Text {
                                text: modelData.groupName
                                font.pixelSize: 16
                                font.bold: true
                                color: "#2c3e50"
                            }
                            
                            GridLayout {
                                Layout.fillWidth: true
                                columns: 2
                                rowSpacing: 10
                                columnSpacing: 20
                                
                                Repeater {
                                    model: modelData.permissions
                                    
                                    RowLayout {
                                        Layout.fillWidth: true
                                        spacing: 10
                                        
                                        CheckBox {
                                            id: permissionCheckbox
                                            checked: hasPermission(permissionsView.selectedRole || "admin", modelData.key)
                                            enabled: permissionsView.selectedRole !== "admin" // Admin always has all permissions
                                            
                                            onCheckedChanged: {
                                                updatePermission(permissionsView.selectedRole || "admin", modelData.key, checked)
                                            }
                                        }
                                        
                                        ColumnLayout {
                                            Layout.fillWidth: true
                                            spacing: 2
                                            
                                            Text {
                                                text: modelData.name
                                                font.pixelSize: 14
                                                color: "#2c3e50"
                                            }
                                            
                                            Text {
                                                text: modelData.description
                                                font.pixelSize: 12
                                                color: "#7f8c8d"
                                                wrapMode: Text.WordWrap
                                                Layout.fillWidth: true
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                // Save button
                Button {
                    Layout.fillWidth: true
                    text: "حفظ التغييرات"
                    
                    background: Rectangle {
                        color: "#27ae60"
                        radius: 5
                    }
                    
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 16
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                    
                    onClicked: savePermissions()
                }
            }
        }
    }
    
    property string selectedRole: "admin"
    
    function getRoleDisplayName(role) {
        switch (role) {
            case "admin": return "مدير"
            case "manager": return "مدير متوسط"
            case "archivist": return "أرشيفي"
            case "viewer": return "مشاهد"
            default: return "غير محدد"
        }
    }
    
    function getPermissionGroups() {
        return [
            {
                groupName: "📄 إدارة المستندات",
                permissions: [
                    { key: "documents.view", name: "عرض المستندات", description: "إمكانية عرض وقراءة المستندات" },
                    { key: "documents.add", name: "إضافة المستندات", description: "إمكانية إضافة مستندات جديدة" },
                    { key: "documents.edit", name: "تحرير المستندات", description: "إمكانية تحرير المستندات الموجودة" },
                    { key: "documents.delete", name: "حذف المستندات", description: "إمكانية حذف المستندات" }
                ]
            },
            {
                groupName: "🔍 البحث والتصفية",
                permissions: [
                    { key: "search.basic", name: "البحث الأساسي", description: "إمكانية البحث في المستندات" },
                    { key: "search.advanced", name: "البحث المتقدم", description: "إمكانية استخدام البحث المتقدم" },
                    { key: "search.export", name: "تصدير نتائج البحث", description: "إمكانية تصدير نتائج البحث" }
                ]
            },
            {
                groupName: "📷 المسح الضوئي",
                permissions: [
                    { key: "scanner.use", name: "استخدام الماسح", description: "إمكانية استخدام الماسح الضوئي" },
                    { key: "scanner.settings", name: "إعدادات المسح", description: "إمكانية تغيير إعدادات المسح" }
                ]
            },
            {
                groupName: "📊 التقارير",
                permissions: [
                    { key: "reports.view", name: "عرض التقارير", description: "إمكانية عرض التقارير" },
                    { key: "reports.generate", name: "إنشاء التقارير", description: "إمكانية إنشاء تقارير جديدة" },
                    { key: "reports.export", name: "تصدير التقارير", description: "إمكانية تصدير التقارير" }
                ]
            },
            {
                groupName: "👥 إدارة المستخدمين",
                permissions: [
                    { key: "users.view", name: "عرض المستخدمين", description: "إمكانية عرض قائمة المستخدمين" },
                    { key: "users.add", name: "إضافة المستخدمين", description: "إمكانية إضافة مستخدمين جدد" },
                    { key: "users.edit", name: "تحرير المستخدمين", description: "إمكانية تحرير بيانات المستخدمين" },
                    { key: "users.delete", name: "حذف المستخدمين", description: "إمكانية حذف المستخدمين" }
                ]
            },
            {
                groupName: "🔐 إدارة الصلاحيات",
                permissions: [
                    { key: "permissions.view", name: "عرض الصلاحيات", description: "إمكانية عرض الصلاحيات" },
                    { key: "permissions.edit", name: "تحرير الصلاحيات", description: "إمكانية تحرير الصلاحيات" }
                ]
            },
            {
                groupName: "⚙️ إعدادات النظام",
                permissions: [
                    { key: "system.settings", name: "إعدادات النظام", description: "إمكانية تغيير إعدادات النظام" },
                    { key: "system.backup", name: "النسخ الاحتياطي", description: "إمكانية إنشاء واستعادة النسخ الاحتياطية" },
                    { key: "system.logs", name: "سجلات النظام", description: "إمكانية عرض سجلات النظام" }
                ]
            }
        ]
    }
    
    function loadRolePermissions(role) {
        // Load default permissions for each role
        var defaultPermissions = {
            "admin": [
                "documents.view", "documents.add", "documents.edit", "documents.delete",
                "search.basic", "search.advanced", "search.export",
                "scanner.use", "scanner.settings",
                "reports.view", "reports.generate", "reports.export",
                "users.view", "users.add", "users.edit", "users.delete",
                "permissions.view", "permissions.edit",
                "system.settings", "system.backup", "system.logs"
            ],
            "manager": [
                "documents.view", "documents.add", "documents.edit",
                "search.basic", "search.advanced", "search.export",
                "scanner.use",
                "reports.view", "reports.generate", "reports.export",
                "users.view", "users.add", "users.edit"
            ],
            "archivist": [
                "documents.view", "documents.add", "documents.edit",
                "search.basic", "search.advanced",
                "scanner.use", "scanner.settings"
            ],
            "viewer": [
                "documents.view",
                "search.basic"
            ]
        }
        
        rolePermissions[role] = defaultPermissions[role] || []
    }
    
    function hasPermission(role, permission) {
        if (role === "admin") return true // Admin always has all permissions
        return rolePermissions[role] && rolePermissions[role].includes(permission)
    }
    
    function updatePermission(role, permission, granted) {
        if (role === "admin") return // Cannot change admin permissions
        
        if (!rolePermissions[role]) {
            rolePermissions[role] = []
        }
        
        if (granted) {
            if (!rolePermissions[role].includes(permission)) {
                rolePermissions[role].push(permission)
            }
        } else {
            var index = rolePermissions[role].indexOf(permission)
            if (index !== -1) {
                rolePermissions[role].splice(index, 1)
            }
        }
    }
    
    function savePermissions() {
        mainWindow.showMessage("نجاح", "تم حفظ الصلاحيات بنجاح", "success")
    }
    
    Component.onCompleted: {
        // Load permissions for all roles
        loadRolePermissions("admin")
        loadRolePermissions("manager")
        loadRolePermissions("archivist")
        loadRolePermissions("viewer")
    }
}
