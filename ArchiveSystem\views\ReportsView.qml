import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: reportsView
    color: "#f8f9fa"
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20
        
        // Header
        RowLayout {
            Layout.fillWidth: true
            
            Text {
                text: "📊 التقارير والإحصائيات"
                font.pixelSize: 24
                font.bold: true
                color: "#2c3e50"
            }
            
            Item {
                Layout.fillWidth: true
            }
            
            Button {
                text: "رجوع"
                
                background: Rectangle {
                    color: "#95a5a6"
                    radius: 5
                }
                
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                
                onClicked: mainWindow.showMainView()
            }
        }
        
        // Statistics cards
        GridLayout {
            Layout.fillWidth: true
            columns: 4
            rowSpacing: 15
            columnSpacing: 15
            
            // Total documents
            Rectangle {
                Layout.fillWidth: true
                height: 100
                color: "#3498db"
                radius: 10
                
                ColumnLayout {
                    anchors.centerIn: parent
                    spacing: 5
                    
                    Text {
                        text: "📄"
                        font.pixelSize: 24
                        Layout.alignment: Qt.AlignHCenter
                    }
                    
                    Text {
                        text: "1,234"
                        color: "white"
                        font.pixelSize: 20
                        font.bold: true
                        Layout.alignment: Qt.AlignHCenter
                    }
                    
                    Text {
                        text: "إجمالي المستندات"
                        color: "#ecf0f1"
                        font.pixelSize: 12
                        Layout.alignment: Qt.AlignHCenter
                    }
                }
            }
            
            // New documents this month
            Rectangle {
                Layout.fillWidth: true
                height: 100
                color: "#27ae60"
                radius: 10
                
                ColumnLayout {
                    anchors.centerIn: parent
                    spacing: 5
                    
                    Text {
                        text: "📈"
                        font.pixelSize: 24
                        Layout.alignment: Qt.AlignHCenter
                    }
                    
                    Text {
                        text: "89"
                        color: "white"
                        font.pixelSize: 20
                        font.bold: true
                        Layout.alignment: Qt.AlignHCenter
                    }
                    
                    Text {
                        text: "مستندات هذا الشهر"
                        color: "#ecf0f1"
                        font.pixelSize: 12
                        Layout.alignment: Qt.AlignHCenter
                    }
                }
            }
            
            // Storage used
            Rectangle {
                Layout.fillWidth: true
                height: 100
                color: "#e67e22"
                radius: 10
                
                ColumnLayout {
                    anchors.centerIn: parent
                    spacing: 5
                    
                    Text {
                        text: "💾"
                        font.pixelSize: 24
                        Layout.alignment: Qt.AlignHCenter
                    }
                    
                    Text {
                        text: "2.4 GB"
                        color: "white"
                        font.pixelSize: 20
                        font.bold: true
                        Layout.alignment: Qt.AlignHCenter
                    }
                    
                    Text {
                        text: "المساحة المستخدمة"
                        color: "#ecf0f1"
                        font.pixelSize: 12
                        Layout.alignment: Qt.AlignHCenter
                    }
                }
            }
            
            // Active users
            Rectangle {
                Layout.fillWidth: true
                height: 100
                color: "#9b59b6"
                radius: 10
                
                ColumnLayout {
                    anchors.centerIn: parent
                    spacing: 5
                    
                    Text {
                        text: "👥"
                        font.pixelSize: 24
                        Layout.alignment: Qt.AlignHCenter
                    }
                    
                    Text {
                        text: "12"
                        color: "white"
                        font.pixelSize: 20
                        font.bold: true
                        Layout.alignment: Qt.AlignHCenter
                    }
                    
                    Text {
                        text: "المستخدمون النشطون"
                        color: "#ecf0f1"
                        font.pixelSize: 12
                        Layout.alignment: Qt.AlignHCenter
                    }
                }
            }
        }
        
        // Reports section
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "white"
            radius: 10
            border.color: "#e0e0e0"
            border.width: 1
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 15
                
                Text {
                    text: "التقارير المتاحة"
                    font.pixelSize: 18
                    font.bold: true
                    color: "#2c3e50"
                }
                
                GridLayout {
                    Layout.fillWidth: true
                    columns: 2
                    rowSpacing: 15
                    columnSpacing: 15
                    
                    // Monthly report
                    Rectangle {
                        Layout.fillWidth: true
                        height: 80
                        color: "#f8f9fa"
                        radius: 8
                        border.color: "#e0e0e0"
                        border.width: 1
                        
                        RowLayout {
                            anchors.fill: parent
                            anchors.margins: 15
                            spacing: 15
                            
                            Text {
                                text: "📅"
                                font.pixelSize: 24
                            }
                            
                            ColumnLayout {
                                Layout.fillWidth: true
                                spacing: 5
                                
                                Text {
                                    text: "التقرير الشهري"
                                    font.pixelSize: 14
                                    font.bold: true
                                    color: "#2c3e50"
                                }
                                
                                Text {
                                    text: "إحصائيات المستندات الشهرية"
                                    font.pixelSize: 12
                                    color: "#7f8c8d"
                                }
                            }
                            
                            Button {
                                text: "إنشاء"
                                
                                background: Rectangle {
                                    color: "#3498db"
                                    radius: 5
                                }
                                
                                contentItem: Text {
                                    text: parent.text
                                    color: "white"
                                    font.pixelSize: 12
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                                
                                onClicked: generateReport("monthly")
                            }
                        }
                    }
                    
                    // Category report
                    Rectangle {
                        Layout.fillWidth: true
                        height: 80
                        color: "#f8f9fa"
                        radius: 8
                        border.color: "#e0e0e0"
                        border.width: 1
                        
                        RowLayout {
                            anchors.fill: parent
                            anchors.margins: 15
                            spacing: 15
                            
                            Text {
                                text: "📂"
                                font.pixelSize: 24
                            }
                            
                            ColumnLayout {
                                Layout.fillWidth: true
                                spacing: 5
                                
                                Text {
                                    text: "تقرير التصنيفات"
                                    font.pixelSize: 14
                                    font.bold: true
                                    color: "#2c3e50"
                                }
                                
                                Text {
                                    text: "توزيع المستندات حسب التصنيف"
                                    font.pixelSize: 12
                                    color: "#7f8c8d"
                                }
                            }
                            
                            Button {
                                text: "إنشاء"
                                
                                background: Rectangle {
                                    color: "#27ae60"
                                    radius: 5
                                }
                                
                                contentItem: Text {
                                    text: parent.text
                                    color: "white"
                                    font.pixelSize: 12
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                                
                                onClicked: generateReport("category")
                            }
                        }
                    }
                    
                    // User activity report
                    Rectangle {
                        Layout.fillWidth: true
                        height: 80
                        color: "#f8f9fa"
                        radius: 8
                        border.color: "#e0e0e0"
                        border.width: 1
                        
                        RowLayout {
                            anchors.fill: parent
                            anchors.margins: 15
                            spacing: 15
                            
                            Text {
                                text: "👤"
                                font.pixelSize: 24
                            }
                            
                            ColumnLayout {
                                Layout.fillWidth: true
                                spacing: 5
                                
                                Text {
                                    text: "تقرير نشاط المستخدمين"
                                    font.pixelSize: 14
                                    font.bold: true
                                    color: "#2c3e50"
                                }
                                
                                Text {
                                    text: "نشاط المستخدمين في النظام"
                                    font.pixelSize: 12
                                    color: "#7f8c8d"
                                }
                            }
                            
                            Button {
                                text: "إنشاء"
                                
                                background: Rectangle {
                                    color: "#e67e22"
                                    radius: 5
                                }
                                
                                contentItem: Text {
                                    text: parent.text
                                    color: "white"
                                    font.pixelSize: 12
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                                
                                onClicked: generateReport("users")
                            }
                        }
                    }
                    
                    // Storage report
                    Rectangle {
                        Layout.fillWidth: true
                        height: 80
                        color: "#f8f9fa"
                        radius: 8
                        border.color: "#e0e0e0"
                        border.width: 1
                        
                        RowLayout {
                            anchors.fill: parent
                            anchors.margins: 15
                            spacing: 15
                            
                            Text {
                                text: "💽"
                                font.pixelSize: 24
                            }
                            
                            ColumnLayout {
                                Layout.fillWidth: true
                                spacing: 5
                                
                                Text {
                                    text: "تقرير التخزين"
                                    font.pixelSize: 14
                                    font.bold: true
                                    color: "#2c3e50"
                                }
                                
                                Text {
                                    text: "استخدام مساحة التخزين"
                                    font.pixelSize: 12
                                    color: "#7f8c8d"
                                }
                            }
                            
                            Button {
                                text: "إنشاء"
                                
                                background: Rectangle {
                                    color: "#9b59b6"
                                    radius: 5
                                }
                                
                                contentItem: Text {
                                    text: parent.text
                                    color: "white"
                                    font.pixelSize: 12
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                                
                                onClicked: generateReport("storage")
                            }
                        }
                    }
                }
            }
        }
    }
    
    function generateReport(type) {
        var reportName = ""
        switch (type) {
            case "monthly":
                reportName = "التقرير الشهري"
                break
            case "category":
                reportName = "تقرير التصنيفات"
                break
            case "users":
                reportName = "تقرير نشاط المستخدمين"
                break
            case "storage":
                reportName = "تقرير التخزين"
                break
        }
        
        mainWindow.showMessage("نجاح", "تم إنشاء " + reportName + " بنجاح", "success")
    }
}
