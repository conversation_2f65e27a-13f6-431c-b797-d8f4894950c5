import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: searchView
    color: "#f8f9fa"
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20
        
        // Header
        RowLayout {
            Layout.fillWidth: true
            
            Text {
                text: "🔍 البحث المتقدم"
                font.pixelSize: 24
                font.bold: true
                color: "#2c3e50"
            }
            
            Item {
                Layout.fillWidth: true
            }
            
            Button {
                text: "رجوع"
                
                background: Rectangle {
                    color: "#95a5a6"
                    radius: 5
                }
                
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                
                onClicked: mainWindow.showMainView()
            }
        }
        
        // Search form
        Rectangle {
            Layout.fillWidth: true
            height: 200
            color: "white"
            radius: 10
            border.color: "#e0e0e0"
            border.width: 1
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 15
                
                Text {
                    text: "معايير البحث"
                    font.pixelSize: 18
                    font.bold: true
                    color: "#2c3e50"
                }
                
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 15
                    
                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 5
                        
                        Text {
                            text: "النص المراد البحث عنه"
                            color: "#2c3e50"
                        }
                        
                        TextField {
                            id: searchTextField
                            Layout.fillWidth: true
                            placeholderText: "أدخل النص للبحث..."
                            
                            background: Rectangle {
                                color: "white"
                                border.color: "#bdc3c7"
                                border.width: 1
                                radius: 5
                            }
                        }
                    }
                    
                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 5
                        
                        Text {
                            text: "التصنيف"
                            color: "#2c3e50"
                        }
                        
                        ComboBox {
                            id: categoryCombo
                            Layout.fillWidth: true
                            model: ["جميع التصنيفات", "عقود", "تقارير مالية", "سياسات", "مراسلات"]
                            
                            background: Rectangle {
                                color: "white"
                                border.color: "#bdc3c7"
                                border.width: 1
                                radius: 5
                            }
                        }
                    }
                }
                
                RowLayout {
                    Layout.fillWidth: true
                    
                    Button {
                        text: "بحث"
                        Layout.fillWidth: true
                        
                        background: Rectangle {
                            color: "#3498db"
                            radius: 5
                        }
                        
                        contentItem: Text {
                            text: parent.text
                            color: "white"
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                        }
                        
                        onClicked: performSearch()
                    }
                    
                    Button {
                        text: "مسح"
                        Layout.fillWidth: true
                        
                        background: Rectangle {
                            color: "#95a5a6"
                            radius: 5
                        }
                        
                        contentItem: Text {
                            text: parent.text
                            color: "white"
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                        }
                        
                        onClicked: clearSearch()
                    }
                }
            }
        }
        
        // Results area
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "white"
            radius: 10
            border.color: "#e0e0e0"
            border.width: 1
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 15
                
                Text {
                    text: "نتائج البحث"
                    font.pixelSize: 18
                    font.bold: true
                    color: "#2c3e50"
                }
                
                Text {
                    id: resultsText
                    text: "أدخل معايير البحث واضغط على زر البحث"
                    color: "#7f8c8d"
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    wrapMode: Text.WordWrap
                    verticalAlignment: Text.AlignTop
                }
            }
        }
    }
    
    function performSearch() {
        var searchText = searchTextField.text
        var category = categoryCombo.currentText
        
        if (searchText.trim() === "") {
            resultsText.text = "يرجى إدخال نص للبحث"
            return
        }
        
        // Simulate search results
        resultsText.text = "نتائج البحث عن: \"" + searchText + "\"\n" +
                          "التصنيف: " + category + "\n\n" +
                          "تم العثور على 3 نتائج:\n\n" +
                          "1. عقد تأسيس الشركة - عقود\n" +
                          "2. تقرير مالي Q1 2024 - تقارير مالية\n" +
                          "3. سياسة الأمان والحماية - سياسات\n\n" +
                          "ملاحظة: هذه نتائج تجريبية. سيتم ربطها بقاعدة البيانات لاحقاً."
    }
    
    function clearSearch() {
        searchTextField.text = ""
        categoryCombo.currentIndex = 0
        resultsText.text = "أدخل معايير البحث واضغط على زر البحث"
    }
}
