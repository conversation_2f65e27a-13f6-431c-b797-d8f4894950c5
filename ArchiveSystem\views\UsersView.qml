import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: usersView
    color: "#f8f9fa"
    
    property var users: []
    property bool isLoading: false
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20
        
        // Header
        RowLayout {
            Layout.fillWidth: true
            
            Text {
                text: "👥 إدارة المستخدمين والصلاحيات"
                font.pixelSize: 24
                font.bold: true
                color: "#2c3e50"
            }
            
            Item {
                Layout.fillWidth: true
            }
            
            Button {
                text: "إدارة الصلاحيات"

                background: Rectangle {
                    color: "#9b59b6"
                    radius: 5
                }

                contentItem: Text {
                    text: parent.text
                    color: "white"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }

                onClicked: mainWindow.showPermissionsView()
            }

            Button {
                text: "إضافة مستخدم جديد"

                background: Rectangle {
                    color: "#27ae60"
                    radius: 5
                }

                contentItem: Text {
                    text: parent.text
                    color: "white"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }

                onClicked: addUserDialog.open()
            }
            
            Button {
                text: "رجوع"
                
                background: Rectangle {
                    color: "#95a5a6"
                    radius: 5
                }
                
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                
                onClicked: mainWindow.showMainView()
            }
        }
        
        // Statistics cards
        RowLayout {
            Layout.fillWidth: true
            spacing: 15
            
            // Total users
            Rectangle {
                Layout.fillWidth: true
                height: 80
                color: "#3498db"
                radius: 10
                
                RowLayout {
                    anchors.fill: parent
                    anchors.margins: 15
                    spacing: 15
                    
                    Text {
                        text: "👤"
                        font.pixelSize: 24
                        color: "white"
                    }
                    
                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 2
                        
                        Text {
                            text: users.length.toString()
                            color: "white"
                            font.pixelSize: 20
                            font.bold: true
                        }
                        
                        Text {
                            text: "إجمالي المستخدمين"
                            color: "#ecf0f1"
                            font.pixelSize: 12
                        }
                    }
                }
            }
            
            // Active users
            Rectangle {
                Layout.fillWidth: true
                height: 80
                color: "#27ae60"
                radius: 10
                
                RowLayout {
                    anchors.fill: parent
                    anchors.margins: 15
                    spacing: 15
                    
                    Text {
                        text: "✅"
                        font.pixelSize: 24
                        color: "white"
                    }
                    
                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 2
                        
                        Text {
                            text: getActiveUsersCount().toString()
                            color: "white"
                            font.pixelSize: 20
                            font.bold: true
                        }
                        
                        Text {
                            text: "المستخدمون النشطون"
                            color: "#ecf0f1"
                            font.pixelSize: 12
                        }
                    }
                }
            }
            
            // Admins
            Rectangle {
                Layout.fillWidth: true
                height: 80
                color: "#e74c3c"
                radius: 10
                
                RowLayout {
                    anchors.fill: parent
                    anchors.margins: 15
                    spacing: 15
                    
                    Text {
                        text: "👑"
                        font.pixelSize: 24
                        color: "white"
                    }
                    
                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 2
                        
                        Text {
                            text: getAdminsCount().toString()
                            color: "white"
                            font.pixelSize: 20
                            font.bold: true
                        }
                        
                        Text {
                            text: "المديرون"
                            color: "#ecf0f1"
                            font.pixelSize: 12
                        }
                    }
                }
            }
        }
        
        // Search and filter
        RowLayout {
            Layout.fillWidth: true
            spacing: 15
            
            TextField {
                id: searchField
                Layout.fillWidth: true
                placeholderText: "البحث في المستخدمين..."
                
                background: Rectangle {
                    color: "white"
                    border.color: "#bdc3c7"
                    border.width: 1
                    radius: 5
                }
                
                onTextChanged: filterUsers()
            }
            
            ComboBox {
                id: roleFilter
                model: ["جميع الأدوار", "مدير", "مدير متوسط", "أرشيفي", "مشاهد"]
                
                background: Rectangle {
                    color: "white"
                    border.color: "#bdc3c7"
                    border.width: 1
                    radius: 5
                }
                
                onCurrentTextChanged: filterUsers()
            }
            
            ComboBox {
                id: statusFilter
                model: ["جميع الحالات", "نشط", "غير نشط"]
                
                background: Rectangle {
                    color: "white"
                    border.color: "#bdc3c7"
                    border.width: 1
                    radius: 5
                }
                
                onCurrentTextChanged: filterUsers()
            }
        }
        
        // Users list
        ScrollView {
            Layout.fillWidth: true
            Layout.fillHeight: true
            
            ListView {
                id: usersListView
                model: users
                spacing: 10
                
                delegate: Rectangle {
                    width: usersListView.width
                    height: 100
                    color: "white"
                    radius: 8
                    border.color: "#e0e0e0"
                    border.width: 1
                    
                    RowLayout {
                        anchors.fill: parent
                        anchors.margins: 15
                        spacing: 15
                        
                        // User avatar
                        Rectangle {
                            width: 70
                            height: 70
                            color: getRoleColor(modelData.role)
                            radius: 35
                            
                            Text {
                                anchors.centerIn: parent
                                text: getRoleIcon(modelData.role)
                                font.pixelSize: 24
                                color: "white"
                            }
                        }
                        
                        // User info
                        ColumnLayout {
                            Layout.fillWidth: true
                            spacing: 5
                            
                            RowLayout {
                                spacing: 10
                                
                                Text {
                                    text: modelData.fullName || "بدون اسم"
                                    font.pixelSize: 16
                                    font.bold: true
                                    color: "#2c3e50"
                                }
                                
                                Rectangle {
                                    width: 60
                                    height: 20
                                    color: modelData.isActive ? "#27ae60" : "#e74c3c"
                                    radius: 10
                                    
                                    Text {
                                        anchors.centerIn: parent
                                        text: modelData.isActive ? "نشط" : "غير نشط"
                                        color: "white"
                                        font.pixelSize: 10
                                    }
                                }
                            }
                            
                            Text {
                                text: "اسم المستخدم: " + (modelData.username || "غير محدد")
                                font.pixelSize: 12
                                color: "#7f8c8d"
                            }
                            
                            Text {
                                text: "الدور: " + getRoleDisplayName(modelData.role)
                                font.pixelSize: 12
                                color: "#7f8c8d"
                            }
                            
                            Text {
                                text: "آخر دخول: " + (modelData.lastLogin || "لم يسجل دخول")
                                font.pixelSize: 12
                                color: "#7f8c8d"
                            }
                        }
                        
                        // Actions
                        ColumnLayout {
                            spacing: 5
                            
                            Button {
                                text: "تحرير"
                                
                                background: Rectangle {
                                    color: "#f39c12"
                                    radius: 3
                                }
                                
                                contentItem: Text {
                                    text: parent.text
                                    color: "white"
                                    font.pixelSize: 12
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                                
                                onClicked: editUser(modelData)
                            }
                            
                            Button {
                                text: modelData.isActive ? "إلغاء تفعيل" : "تفعيل"
                                
                                background: Rectangle {
                                    color: modelData.isActive ? "#e74c3c" : "#27ae60"
                                    radius: 3
                                }
                                
                                contentItem: Text {
                                    text: parent.text
                                    color: "white"
                                    font.pixelSize: 12
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                                
                                onClicked: toggleUserStatus(modelData)
                            }
                            
                            Button {
                                text: "حذف"
                                enabled: modelData.username !== "admin" // لا يمكن حذف المدير الرئيسي
                                
                                background: Rectangle {
                                    color: parent.enabled ? "#e74c3c" : "#bdc3c7"
                                    radius: 3
                                }
                                
                                contentItem: Text {
                                    text: parent.text
                                    color: "white"
                                    font.pixelSize: 12
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                                
                                onClicked: deleteUser(modelData)
                            }
                        }
                    }
                }
            }
        }
        
        // Empty state
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "transparent"
            visible: users.length === 0 && !isLoading
            
            ColumnLayout {
                anchors.centerIn: parent
                spacing: 20
                
                Text {
                    text: "👥"
                    font.pixelSize: 64
                    Layout.alignment: Qt.AlignHCenter
                }
                
                Text {
                    text: "لا يوجد مستخدمون"
                    font.pixelSize: 18
                    color: "#7f8c8d"
                    Layout.alignment: Qt.AlignHCenter
                }
                
                Text {
                    text: "ابدأ بإضافة مستخدم جديد"
                    font.pixelSize: 14
                    color: "#95a5a6"
                    Layout.alignment: Qt.AlignHCenter
                }
            }
        }
    }

    // Add User Dialog
    Dialog {
        id: addUserDialog
        title: "إضافة مستخدم جديد"
        modal: true
        anchors.centerIn: parent
        width: 450
        height: 500

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 15

            // Full name
            ColumnLayout {
                Layout.fillWidth: true
                spacing: 5

                Text {
                    text: "الاسم الكامل *"
                    color: "#2c3e50"
                    font.bold: true
                }

                TextField {
                    id: fullNameField
                    Layout.fillWidth: true
                    placeholderText: "أدخل الاسم الكامل"

                    background: Rectangle {
                        color: "white"
                        border.color: "#bdc3c7"
                        border.width: 1
                        radius: 5
                    }
                }
            }

            // Username
            ColumnLayout {
                Layout.fillWidth: true
                spacing: 5

                Text {
                    text: "اسم المستخدم *"
                    color: "#2c3e50"
                    font.bold: true
                }

                TextField {
                    id: usernameField
                    Layout.fillWidth: true
                    placeholderText: "أدخل اسم المستخدم"

                    background: Rectangle {
                        color: "white"
                        border.color: "#bdc3c7"
                        border.width: 1
                        radius: 5
                    }
                }
            }

            // Email
            ColumnLayout {
                Layout.fillWidth: true
                spacing: 5

                Text {
                    text: "البريد الإلكتروني"
                    color: "#2c3e50"
                    font.bold: true
                }

                TextField {
                    id: emailField
                    Layout.fillWidth: true
                    placeholderText: "أدخل البريد الإلكتروني"

                    background: Rectangle {
                        color: "white"
                        border.color: "#bdc3c7"
                        border.width: 1
                        radius: 5
                    }
                }
            }

            // Password
            ColumnLayout {
                Layout.fillWidth: true
                spacing: 5

                Text {
                    text: "كلمة المرور *"
                    color: "#2c3e50"
                    font.bold: true
                }

                TextField {
                    id: passwordField
                    Layout.fillWidth: true
                    placeholderText: "أدخل كلمة المرور"
                    echoMode: TextInput.Password

                    background: Rectangle {
                        color: "white"
                        border.color: "#bdc3c7"
                        border.width: 1
                        radius: 5
                    }
                }
            }

            // Role
            ColumnLayout {
                Layout.fillWidth: true
                spacing: 5

                Text {
                    text: "الدور *"
                    color: "#2c3e50"
                    font.bold: true
                }

                ComboBox {
                    id: roleCombo
                    Layout.fillWidth: true
                    model: ["admin", "manager", "archivist", "viewer"]
                    displayText: getRoleDisplayName(currentText)

                    background: Rectangle {
                        color: "white"
                        border.color: "#bdc3c7"
                        border.width: 1
                        radius: 5
                    }
                }
            }

            // Active status
            RowLayout {
                Layout.fillWidth: true

                CheckBox {
                    id: activeCheckbox
                    checked: true
                }

                Text {
                    text: "المستخدم نشط"
                    color: "#2c3e50"
                }
            }

            // Buttons
            RowLayout {
                Layout.fillWidth: true
                spacing: 10

                Button {
                    text: "إلغاء"
                    Layout.fillWidth: true

                    background: Rectangle {
                        color: "#95a5a6"
                        radius: 5
                    }

                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }

                    onClicked: {
                        clearForm()
                        addUserDialog.close()
                    }
                }

                Button {
                    text: "إضافة"
                    Layout.fillWidth: true

                    background: Rectangle {
                        color: "#27ae60"
                        radius: 5
                    }

                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }

                    onClicked: addUser()
                }
            }
        }
    }

    // Functions
    function loadUsers() {
        // Load sample users data
        users = [
            {
                id: 1,
                username: "admin",
                fullName: "المدير العام",
                email: "<EMAIL>",
                role: "admin",
                isActive: true,
                lastLogin: "2024-07-08 10:30",
                createdDate: "2024-01-01"
            },
            {
                id: 2,
                username: "manager1",
                fullName: "أحمد محمد",
                email: "<EMAIL>",
                role: "manager",
                isActive: true,
                lastLogin: "2024-07-08 09:15",
                createdDate: "2024-02-15"
            },
            {
                id: 3,
                username: "archivist1",
                fullName: "فاطمة علي",
                email: "<EMAIL>",
                role: "archivist",
                isActive: true,
                lastLogin: "2024-07-07 16:45",
                createdDate: "2024-03-10"
            },
            {
                id: 4,
                username: "viewer1",
                fullName: "محمد حسن",
                email: "<EMAIL>",
                role: "viewer",
                isActive: false,
                lastLogin: "2024-07-05 14:20",
                createdDate: "2024-04-20"
            }
        ]
    }

    function getRoleDisplayName(role) {
        switch (role) {
            case "admin": return "مدير"
            case "manager": return "مدير متوسط"
            case "archivist": return "أرشيفي"
            case "viewer": return "مشاهد"
            default: return "غير محدد"
        }
    }

    function getRoleIcon(role) {
        switch (role) {
            case "admin": return "👑"
            case "manager": return "👔"
            case "archivist": return "📁"
            case "viewer": return "👁"
            default: return "👤"
        }
    }

    function getRoleColor(role) {
        switch (role) {
            case "admin": return "#e74c3c"
            case "manager": return "#3498db"
            case "archivist": return "#f39c12"
            case "viewer": return "#95a5a6"
            default: return "#7f8c8d"
        }
    }

    function getActiveUsersCount() {
        return users.filter(function(user) { return user.isActive }).length
    }

    function getAdminsCount() {
        return users.filter(function(user) { return user.role === "admin" }).length
    }

    function addUser() {
        if (fullNameField.text.trim() === "" || usernameField.text.trim() === "" || passwordField.text.trim() === "") {
            mainWindow.showMessage("خطأ", "يرجى ملء جميع الحقول المطلوبة", "error")
            return
        }

        // Check if username already exists
        var existingUser = users.find(function(user) { return user.username === usernameField.text })
        if (existingUser) {
            mainWindow.showMessage("خطأ", "اسم المستخدم موجود بالفعل", "error")
            return
        }

        var newUser = {
            id: users.length + 1,
            username: usernameField.text,
            fullName: fullNameField.text,
            email: emailField.text,
            role: roleCombo.currentText,
            isActive: activeCheckbox.checked,
            lastLogin: "لم يسجل دخول",
            createdDate: Qt.formatDate(new Date(), "yyyy-MM-dd")
        }

        users.push(newUser)
        usersListView.model = users

        clearForm()
        addUserDialog.close()

        mainWindow.showMessage("نجاح", "تم إضافة المستخدم بنجاح", "success")
    }

    function editUser(user) {
        fullNameField.text = user.fullName
        usernameField.text = user.username
        emailField.text = user.email
        passwordField.text = ""

        // Set role combo
        for (var i = 0; i < roleCombo.model.length; i++) {
            if (roleCombo.model[i] === user.role) {
                roleCombo.currentIndex = i
                break
            }
        }

        activeCheckbox.checked = user.isActive
        addUserDialog.title = "تحرير المستخدم"
        addUserDialog.open()
    }

    function toggleUserStatus(user) {
        if (user.username === "admin") {
            mainWindow.showMessage("تحذير", "لا يمكن إلغاء تفعيل المدير الرئيسي", "warning")
            return
        }

        var index = users.findIndex(function(u) { return u.id === user.id })
        if (index !== -1) {
            users[index].isActive = !users[index].isActive
            usersListView.model = users

            var status = users[index].isActive ? "تم تفعيل" : "تم إلغاء تفعيل"
            mainWindow.showMessage("نجاح", status + " المستخدم بنجاح", "success")
        }
    }

    function deleteUser(user) {
        if (user.username === "admin") {
            mainWindow.showMessage("تحذير", "لا يمكن حذف المدير الرئيسي", "warning")
            return
        }

        var index = users.findIndex(function(u) { return u.id === user.id })
        if (index !== -1) {
            users.splice(index, 1)
            usersListView.model = users
            mainWindow.showMessage("نجاح", "تم حذف المستخدم بنجاح", "success")
        }
    }

    function filterUsers() {
        var searchTerm = searchField.text.toLowerCase()
        var selectedRole = roleFilter.currentText
        var selectedStatus = statusFilter.currentText

        var filteredUsers = users.filter(function(user) {
            var matchesSearch = searchTerm === "" ||
                               user.fullName.toLowerCase().includes(searchTerm) ||
                               user.username.toLowerCase().includes(searchTerm) ||
                               user.email.toLowerCase().includes(searchTerm)

            var matchesRole = selectedRole === "جميع الأدوار" ||
                             getRoleDisplayName(user.role) === selectedRole

            var matchesStatus = selectedStatus === "جميع الحالات" ||
                               (selectedStatus === "نشط" && user.isActive) ||
                               (selectedStatus === "غير نشط" && !user.isActive)

            return matchesSearch && matchesRole && matchesStatus
        })

        usersListView.model = filteredUsers
    }

    function clearForm() {
        fullNameField.text = ""
        usernameField.text = ""
        emailField.text = ""
        passwordField.text = ""
        roleCombo.currentIndex = 0
        activeCheckbox.checked = true
        addUserDialog.title = "إضافة مستخدم جديد"
    }

    Component.onCompleted: {
        loadUsers()
    }
}
