import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: mainView
    color: "#f8f9fa"
    
    ColumnLayout {
        anchors.fill: parent
        spacing: 0
        
        // Header
        Rectangle {
            Layout.fillWidth: true
            height: 60
            color: "#2c3e50"
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 20
                
                Text {
                    text: "🏦 نظام الأرشفة المصرفي"
                    color: "white"
                    font.pixelSize: 20
                    font.bold: true
                }
                
                Item {
                    Layout.fillWidth: true
                }
                
                Text {
                    text: "مرحباً، " + (mainWindow.currentUser || "المستخدم")
                    color: "white"
                    font.pixelSize: 14
                }
                
                Button {
                    text: "تسجيل الخروج"
                    onClicked: {
                        mainWindow.logout()
                    }
                    
                    background: Rectangle {
                        color: "#e74c3c"
                        radius: 5
                    }
                    
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
            }
        }
        
        // Main content area
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "#ecf0f1"
            
            ColumnLayout {
                anchors.centerIn: parent
                spacing: 30
                
                Text {
                    text: "مرحباً بك في نظام الأرشفة المصرفي"
                    font.pixelSize: 28
                    font.bold: true
                    color: "#2c3e50"
                    Layout.alignment: Qt.AlignHCenter
                }
                
                Text {
                    text: "تم تسجيل الدخول بنجاح!"
                    font.pixelSize: 18
                    color: "#27ae60"
                    Layout.alignment: Qt.AlignHCenter
                }
                
                // Feature cards
                GridLayout {
                    columns: 2
                    rowSpacing: 20
                    columnSpacing: 20
                    Layout.alignment: Qt.AlignHCenter
                    
                    // Documents card
                    Rectangle {
                        width: 200
                        height: 150
                        color: "#3498db"
                        radius: 10
                        
                        ColumnLayout {
                            anchors.centerIn: parent
                            spacing: 10
                            
                            Text {
                                text: "📄"
                                font.pixelSize: 40
                                Layout.alignment: Qt.AlignHCenter
                            }
                            
                            Text {
                                text: "إدارة المستندات"
                                color: "white"
                                font.pixelSize: 16
                                font.bold: true
                                Layout.alignment: Qt.AlignHCenter
                            }
                            
                            Text {
                                text: "إضافة وتحرير المستندات"
                                color: "#ecf0f1"
                                font.pixelSize: 12
                                Layout.alignment: Qt.AlignHCenter
                            }
                        }
                        
                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                mainWindow.showDocumentsView()
                            }
                        }
                    }
                    
                    // Search card
                    Rectangle {
                        width: 200
                        height: 150
                        color: "#9b59b6"
                        radius: 10
                        
                        ColumnLayout {
                            anchors.centerIn: parent
                            spacing: 10
                            
                            Text {
                                text: "🔍"
                                font.pixelSize: 40
                                Layout.alignment: Qt.AlignHCenter
                            }
                            
                            Text {
                                text: "البحث المتقدم"
                                color: "white"
                                font.pixelSize: 16
                                font.bold: true
                                Layout.alignment: Qt.AlignHCenter
                            }
                            
                            Text {
                                text: "البحث في المستندات"
                                color: "#ecf0f1"
                                font.pixelSize: 12
                                Layout.alignment: Qt.AlignHCenter
                            }
                        }
                        
                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                mainWindow.showSearchView()
                            }
                        }
                    }
                    
                    // Scanner card
                    Rectangle {
                        width: 200
                        height: 150
                        color: "#e67e22"
                        radius: 10
                        
                        ColumnLayout {
                            anchors.centerIn: parent
                            spacing: 10
                            
                            Text {
                                text: "📷"
                                font.pixelSize: 40
                                Layout.alignment: Qt.AlignHCenter
                            }
                            
                            Text {
                                text: "المسح الضوئي"
                                color: "white"
                                font.pixelSize: 16
                                font.bold: true
                                Layout.alignment: Qt.AlignHCenter
                            }
                            
                            Text {
                                text: "مسح المستندات ضوئياً"
                                color: "#ecf0f1"
                                font.pixelSize: 12
                                Layout.alignment: Qt.AlignHCenter
                            }
                        }
                        
                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                mainWindow.showScannerView()
                            }
                        }
                    }
                    
                    // Reports card
                    Rectangle {
                        width: 200
                        height: 150
                        color: "#1abc9c"
                        radius: 10
                        
                        ColumnLayout {
                            anchors.centerIn: parent
                            spacing: 10
                            
                            Text {
                                text: "📊"
                                font.pixelSize: 40
                                Layout.alignment: Qt.AlignHCenter
                            }
                            
                            Text {
                                text: "التقارير"
                                color: "white"
                                font.pixelSize: 16
                                font.bold: true
                                Layout.alignment: Qt.AlignHCenter
                            }
                            
                            Text {
                                text: "تقارير وإحصائيات"
                                color: "#ecf0f1"
                                font.pixelSize: 12
                                Layout.alignment: Qt.AlignHCenter
                            }
                        }
                        
                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                mainWindow.showReportsView()
                            }
                        }
                    }
                }
            }
        }
    }

    // Function to show main view (used by other views to return)
    function showMainView() {
        // This function is called by child views to return to main view
        // The actual navigation is handled by the parent window
    }
}
