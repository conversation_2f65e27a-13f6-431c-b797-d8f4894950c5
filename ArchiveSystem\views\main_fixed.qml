import QtQuick 2.15
import QtQuick.Window 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import Qt.labs.platform 1.1 as Platform

ApplicationWindow {
    id: mainWindow
    visible: true
    width: 1280
    height: 800
    title: appController.getAppName()
    
    // RTL support
    LayoutMirroring.enabled: appController.isRtl
    LayoutMirroring.childrenInherit: true
    
    // Properties
    property bool isLoggedIn: false
    property string currentUser: ""
    property int currentUserId: 0
    property string currentUserRole: ""
    property string currentView: "main" // main, documents, search, scanner, reports, users, permissions
    
    // Theme colors
    property color primaryColor: "#2c3e50"
    property color secondaryColor: "#3498db"
    property color accentColor: "#e74c3c"
    property color backgroundColor: "#ecf0f1"
    property color textColor: "#2c3e50"
    property color lightTextColor: "#7f8c8d"
    property color successColor: "#27ae60"
    property color warningColor: "#f39c12"
    property color errorColor: "#e74c3c"
    
    // Fonts
    property int headerFontSize: 24
    property int titleFontSize: 18
    property int bodyFontSize: 14
    property int captionFontSize: 12
    
    // Spacing
    property int smallSpacing: 5
    property int mediumSpacing: 10
    property int largeSpacing: 20
    
    // Border radius
    property int borderRadius: 5
    
    // Functions
    function formatDate(date) {
        if (!date || isNaN(date.getTime())) {
            return ""
        }
        return Qt.formatDate(date, "yyyy-MM-dd")
    }
    
    function formatDateTime(date) {
        if (!date || isNaN(date.getTime())) {
            return ""
        }
        return Qt.formatDateTime(date, "yyyy-MM-dd hh:mm")
    }
    
    function showMessage(title, message, type) {
        messageDialog.title = title
        messageDialog.messageText = message
        messageDialog.open()
    }
    
    // Main content loader
    Loader {
        id: contentLoader
        anchors.fill: parent
        source: {
            if (!isLoggedIn) {
                return "LoginView_fixed.qml"
            }

            switch (currentView) {
                case "documents":
                    return "DocumentsView.qml"
                case "search":
                    return "SearchView.qml"
                case "scanner":
                    return "ScannerView.qml"
                case "reports":
                    return "ReportsView.qml"
                case "users":
                    return "UsersView.qml"
                case "permissions":
                    return "PermissionsView.qml"
                default:
                    return "MainView_simple.qml"
            }
        }
    }
    
    // Global message dialog
    Dialog {
        id: messageDialog
        title: "رسالة"
        modal: true
        anchors.centerIn: parent
        width: 300
        height: 150
        
        property alias messageText: messageLabel.text
        
        Column {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 20
            
            Text {
                id: messageLabel
                width: parent.width
                wrapMode: Text.WordWrap
                text: ""
                color: textColor
            }
            
            Button {
                text: "موافق"
                anchors.horizontalCenter: parent.horizontalCenter
                onClicked: messageDialog.close()
            }
        }
        
        function showError(message) {
            title = "خطأ"
            messageText = message
            open()
        }
        
        function showInfo(message) {
            title = "معلومات"
            messageText = message
            open()
        }
        
        function showSuccess(message) {
            title = "نجاح"
            messageText = message
            open()
        }
        
        function showWarning(message) {
            title = "تحذير"
            messageText = message
            open()
        }
    }
    
    // Global file dialog for opening files
    Platform.FileDialog {
        id: openFileDialog
        title: "اختر ملفًا"
        folder: Platform.StandardPaths.writableLocation(Platform.StandardPaths.DocumentsLocation)
        nameFilters: ["جميع الملفات (*.*)"]
        
        property var callback: null
        
        onAccepted: {
            if (callback) {
                callback(file)
            }
        }
        
        function openForCallback(cb, filters) {
            callback = cb
            if (filters) {
                nameFilters = filters
            }
            open()
        }
    }
    
    // Global file dialog for saving files
    Platform.FileDialog {
        id: saveFileDialog
        title: "حفظ الملف"
        folder: Platform.StandardPaths.writableLocation(Platform.StandardPaths.DocumentsLocation)
        fileMode: Platform.FileDialog.SaveFile
        
        property var callback: null
        
        onAccepted: {
            if (callback) {
                callback(file)
            }
        }
        
        function saveForCallback(cb, defaultFileName, filters) {
            callback = cb
            if (filters) {
                nameFilters = filters
            }
            open()
        }
    }
    
    // Navigation functions
    function showMainView() {
        currentView = "main"
    }

    function showDocumentsView() {
        currentView = "documents"
    }

    function showSearchView() {
        currentView = "search"
    }

    function showScannerView() {
        currentView = "scanner"
    }

    function showReportsView() {
        currentView = "reports"
    }

    function showUsersView() {
        currentView = "users"
    }

    function showPermissionsView() {
        currentView = "permissions"
    }

    // Login functions
    function login(username, password) {
        userViewModel.login(username, password)
    }

    function logout() {
        isLoggedIn = false
        currentUser = ""
        currentUserId = 0
        currentUserRole = ""
        currentView = "main"
        userViewModel.logout()
    }
    
    // User ViewModel connections (commented out until signals are implemented)
    /*
    Connections {
        target: userViewModel

        function onLoginSuccess(user) {
            isLoggedIn = true
            currentUser = user.username
            currentUserId = user.id
            currentUserRole = user.role
            messageDialog.showSuccess("تم تسجيل الدخول بنجاح")
        }

        function onLoginFailed(error) {
            messageDialog.showError("فشل تسجيل الدخول: " + error)
        }

        function onLogoutSuccess() {
            messageDialog.showInfo("تم تسجيل الخروج بنجاح")
        }
    }
    */
    
    // ViewModel connections (commented out until signals are implemented)
    /*
    // Document ViewModel connections
    Connections {
        target: documentViewModel

        function onDocumentAdded(document) {
            messageDialog.showSuccess("تم إضافة المستند بنجاح")
        }

        function onDocumentUpdated(document) {
            messageDialog.showSuccess("تم تحديث المستند بنجاح")
        }

        function onDocumentDeleted(documentId) {
            messageDialog.showSuccess("تم حذف المستند بنجاح")
        }

        function onError(error) {
            messageDialog.showError("خطأ: " + error)
        }
    }

    // Category ViewModel connections
    Connections {
        target: categoryViewModel

        function onCategoryAdded(category) {
            messageDialog.showSuccess("تم إضافة التصنيف بنجاح")
        }

        function onCategoryUpdated(category) {
            messageDialog.showSuccess("تم تحديث التصنيف بنجاح")
        }

        function onCategoryDeleted(categoryId) {
            messageDialog.showSuccess("تم حذف التصنيف بنجاح")
        }

        function onError(error) {
            messageDialog.showError("خطأ: " + error)
        }
    }

    // Scanner ViewModel connections
    Connections {
        target: scannerViewModel

        function onScanCompleted(imagePath) {
            messageDialog.showSuccess("تم المسح الضوئي بنجاح")
        }

        function onScanFailed(error) {
            messageDialog.showError("فشل المسح الضوئي: " + error)
        }
    }
    */
    
    // Component.onCompleted
    Component.onCompleted: {
        // Initialize the application
        console.log("Banking Archive System started")

        // For now, start with login screen
        // TODO: Check if user is already logged in when ViewModels are ready
        /*
        if (userViewModel.isLoggedIn()) {
            var user = userViewModel.getCurrentUser()
            if (user) {
                isLoggedIn = true
                currentUser = user.username
                currentUserId = user.id
                currentUserRole = user.role
            }
        }
        */
    }
}
