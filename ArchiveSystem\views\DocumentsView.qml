import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import Qt.labs.platform 1.1 as Platform

Rectangle {
    id: documentsView
    color: "#f8f9fa"
    
    property var documents: []
    property bool isLoading: false
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20
        
        // Header
        RowLayout {
            Layout.fillWidth: true
            
            Text {
                text: "📄 إدارة المستندات"
                font.pixelSize: 24
                font.bold: true
                color: "#2c3e50"
            }
            
            Item {
                Layout.fillWidth: true
            }
            
            Button {
                text: "إضافة مستند جديد"
                
                background: Rectangle {
                    color: "#27ae60"
                    radius: 5
                }
                
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                
                onClicked: addDocumentDialog.open()
            }
            
            Button {
                text: "رجوع"
                
                background: Rectangle {
                    color: "#95a5a6"
                    radius: 5
                }
                
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                
                onClicked: mainWindow.showMainView()
            }
        }
        
        // Search bar
        RowLayout {
            Layout.fillWidth: true
            
            TextField {
                id: searchField
                Layout.fillWidth: true
                placeholderText: "البحث في المستندات..."
                
                background: Rectangle {
                    color: "white"
                    border.color: "#bdc3c7"
                    border.width: 1
                    radius: 5
                }
            }
            
            Button {
                text: "بحث"
                
                background: Rectangle {
                    color: "#3498db"
                    radius: 5
                }
                
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                
                onClicked: searchDocuments()
            }
        }
        
        // Documents list
        ScrollView {
            Layout.fillWidth: true
            Layout.fillHeight: true
            
            ListView {
                id: documentsListView
                model: documents
                spacing: 10
                
                delegate: Rectangle {
                    width: documentsListView.width
                    height: 80
                    color: "white"
                    radius: 8
                    border.color: "#e0e0e0"
                    border.width: 1
                    
                    RowLayout {
                        anchors.fill: parent
                        anchors.margins: 15
                        spacing: 15
                        
                        // Document icon
                        Rectangle {
                            width: 50
                            height: 50
                            color: "#3498db"
                            radius: 25
                            
                            Text {
                                anchors.centerIn: parent
                                text: "📄"
                                font.pixelSize: 20
                            }
                        }
                        
                        // Document info
                        ColumnLayout {
                            Layout.fillWidth: true
                            spacing: 5
                            
                            Text {
                                text: modelData.title || "مستند بدون عنوان"
                                font.pixelSize: 16
                                font.bold: true
                                color: "#2c3e50"
                            }
                            
                            Text {
                                text: "التصنيف: " + (modelData.category || "غير محدد")
                                font.pixelSize: 12
                                color: "#7f8c8d"
                            }
                            
                            Text {
                                text: "تاريخ الإضافة: " + (modelData.dateAdded || "غير محدد")
                                font.pixelSize: 12
                                color: "#7f8c8d"
                            }
                        }
                        
                        // Actions
                        RowLayout {
                            spacing: 10
                            
                            Button {
                                text: "عرض"
                                
                                background: Rectangle {
                                    color: "#3498db"
                                    radius: 3
                                }
                                
                                contentItem: Text {
                                    text: parent.text
                                    color: "white"
                                    font.pixelSize: 12
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                                
                                onClicked: viewDocument(modelData)
                            }
                            
                            Button {
                                text: "تحرير"
                                
                                background: Rectangle {
                                    color: "#f39c12"
                                    radius: 3
                                }
                                
                                contentItem: Text {
                                    text: parent.text
                                    color: "white"
                                    font.pixelSize: 12
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                                
                                onClicked: editDocument(modelData)
                            }
                            
                            Button {
                                text: "حذف"
                                
                                background: Rectangle {
                                    color: "#e74c3c"
                                    radius: 3
                                }
                                
                                contentItem: Text {
                                    text: parent.text
                                    color: "white"
                                    font.pixelSize: 12
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                                
                                onClicked: deleteDocument(modelData)
                            }
                        }
                    }
                }
            }
        }
        
        // Empty state
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "transparent"
            visible: documents.length === 0 && !isLoading
            
            ColumnLayout {
                anchors.centerIn: parent
                spacing: 20
                
                Text {
                    text: "📄"
                    font.pixelSize: 64
                    Layout.alignment: Qt.AlignHCenter
                }
                
                Text {
                    text: "لا توجد مستندات"
                    font.pixelSize: 18
                    color: "#7f8c8d"
                    Layout.alignment: Qt.AlignHCenter
                }
                
                Text {
                    text: "ابدأ بإضافة مستند جديد"
                    font.pixelSize: 14
                    color: "#95a5a6"
                    Layout.alignment: Qt.AlignHCenter
                }
            }
        }
    }
    
    // Add Document Dialog
    Dialog {
        id: addDocumentDialog
        title: "إضافة مستند جديد"
        modal: true
        anchors.centerIn: parent
        width: 400
        height: 300
        
        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 15
            
            TextField {
                id: titleField
                Layout.fillWidth: true
                placeholderText: "عنوان المستند"
                
                background: Rectangle {
                    color: "white"
                    border.color: "#bdc3c7"
                    border.width: 1
                    radius: 5
                }
            }
            
            TextField {
                id: categoryField
                Layout.fillWidth: true
                placeholderText: "التصنيف"
                
                background: Rectangle {
                    color: "white"
                    border.color: "#bdc3c7"
                    border.width: 1
                    radius: 5
                }
            }
            
            ScrollView {
                Layout.fillWidth: true
                Layout.fillHeight: true
                
                TextArea {
                    id: descriptionField
                    placeholderText: "وصف المستند..."
                    wrapMode: TextArea.Wrap
                    
                    background: Rectangle {
                        color: "white"
                        border.color: "#bdc3c7"
                        border.width: 1
                        radius: 5
                    }
                }
            }
            
            RowLayout {
                Layout.fillWidth: true
                
                Button {
                    text: "إلغاء"
                    Layout.fillWidth: true
                    
                    background: Rectangle {
                        color: "#95a5a6"
                        radius: 5
                    }
                    
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                    
                    onClicked: addDocumentDialog.close()
                }
                
                Button {
                    text: "إضافة"
                    Layout.fillWidth: true
                    
                    background: Rectangle {
                        color: "#27ae60"
                        radius: 5
                    }
                    
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                    
                    onClicked: addDocument()
                }
            }
        }
    }
    
    // Functions
    function loadDocuments() {
        // Simulate loading documents
        documents = [
            {
                id: 1,
                title: "عقد تأسيس الشركة",
                category: "عقود",
                description: "عقد تأسيس الشركة الأساسي",
                dateAdded: "2024-01-15"
            },
            {
                id: 2,
                title: "تقرير مالي Q1 2024",
                category: "تقارير مالية",
                description: "التقرير المالي للربع الأول من عام 2024",
                dateAdded: "2024-03-31"
            },
            {
                id: 3,
                title: "سياسة الأمان والحماية",
                category: "سياسات",
                description: "سياسة الأمان وحماية البيانات",
                dateAdded: "2024-02-10"
            }
        ]
    }
    
    function addDocument() {
        if (titleField.text.trim() === "") {
            mainWindow.showMessage("خطأ", "يرجى إدخال عنوان المستند", "error")
            return
        }
        
        var newDoc = {
            id: documents.length + 1,
            title: titleField.text,
            category: categoryField.text || "غير محدد",
            description: descriptionField.text,
            dateAdded: Qt.formatDate(new Date(), "yyyy-MM-dd")
        }
        
        documents.push(newDoc)
        documentsListView.model = documents
        
        titleField.text = ""
        categoryField.text = ""
        descriptionField.text = ""
        addDocumentDialog.close()
        
        mainWindow.showMessage("نجاح", "تم إضافة المستند بنجاح", "success")
    }
    
    function searchDocuments() {
        var searchTerm = searchField.text.toLowerCase()
        if (searchTerm === "") {
            loadDocuments()
            return
        }
        
        var filteredDocs = documents.filter(function(doc) {
            return doc.title.toLowerCase().includes(searchTerm) ||
                   doc.category.toLowerCase().includes(searchTerm) ||
                   doc.description.toLowerCase().includes(searchTerm)
        })
        
        documentsListView.model = filteredDocs
    }
    
    function viewDocument(doc) {
        mainWindow.showMessage("عرض المستند", "عنوان: " + doc.title + "\nالتصنيف: " + doc.category + "\nالوصف: " + doc.description, "info")
    }
    
    function editDocument(doc) {
        titleField.text = doc.title
        categoryField.text = doc.category
        descriptionField.text = doc.description
        addDocumentDialog.title = "تحرير المستند"
        addDocumentDialog.open()
    }
    
    function deleteDocument(doc) {
        var index = documents.findIndex(function(d) { return d.id === doc.id })
        if (index !== -1) {
            documents.splice(index, 1)
            documentsListView.model = documents
            mainWindow.showMessage("نجاح", "تم حذف المستند بنجاح", "success")
        }
    }
    
    Component.onCompleted: {
        loadDocuments()
    }
}
