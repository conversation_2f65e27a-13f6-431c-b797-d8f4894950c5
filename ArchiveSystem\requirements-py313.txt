# Requirements for Python 3.13 (experimental)
# Note: Some packages may not be fully compatible with Python 3.13 yet

# Core GUI Framework - using latest available version
PySide6>=6.8.0

# Database
mysql-connector-python>=8.0.32

# Image Processing
pillow>=10.0.0
opencv-python>=4.8.0

# OCR and PDF processing
pytesseract>=0.3.10
PyMuPDF>=1.23.0

# Configuration
python-dotenv>=1.0.0

# Security
bcrypt>=4.0.1

# Document Generation
reportlab>=4.0.0
qrcode>=7.4.2

# Utilities
python-dateutil>=2.8.2

# Windows-specific packages
pywin32>=306; platform_system == "Windows"

# Scanner support alternatives for Python 3.13
# Note: pyinsane2 may not work with Python 3.13
# Consider using alternative scanner libraries or manual scanner integration
