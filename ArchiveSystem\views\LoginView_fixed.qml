import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: loginView
    color: parent.backgroundColor || "#ecf0f1"
    
    property bool isLoading: false
    
    // Local color properties
    property color surfaceColor: "#ffffff"
    property color dividerColor: "#bdc3c7"
    property color secondaryTextColor: "#7f8c8d"
    
    // Local font size properties
    property int fontSizeHeader: 24
    property int fontSizeLarge: 18
    property int fontSizeMedium: 16
    property int fontSizeNormal: 14
    property int fontSizeSmall: 12
    
    ColumnLayout {
        anchors.centerIn: parent
        width: Math.min(parent.width * 0.8, 400)
        spacing: 20
        
        // Logo and title
        ColumnLayout {
            Layout.alignment: Qt.AlignHCenter
            spacing: 10
            
            // Logo placeholder (since image file doesn't exist)
            Rectangle {
                width: 100
                height: 100
                color: "#3498db"
                radius: 50
                Layout.alignment: Qt.AlignHCenter
                
                Text {
                    anchors.centerIn: parent
                    text: "🏦"
                    font.pixelSize: 40
                    color: "white"
                }
            }
            
            Text {
                text: "نظام الأرشفة المصرفي"
                font.pixelSize: fontSizeHeader
                font.bold: true
                color: parent.parent.parent.textColor || "#2c3e50"
                Layout.alignment: Qt.AlignHCenter
            }
            
            Text {
                text: "تسجيل الدخول إلى النظام"
                font.pixelSize: fontSizeMedium
                color: secondaryTextColor
                Layout.alignment: Qt.AlignHCenter
            }
        }
        
        // Login form
        Rectangle {
            Layout.fillWidth: true
            height: 300
            color: surfaceColor
            radius: 10
            border.color: dividerColor
            border.width: 1
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 30
                spacing: 20
                
                Text {
                    text: "تسجيل الدخول"
                    font.pixelSize: fontSizeLarge
                    font.bold: true
                    color: parent.parent.parent.textColor || "#2c3e50"
                    Layout.alignment: Qt.AlignHCenter
                }
                
                // Username field
                ColumnLayout {
                    Layout.fillWidth: true
                    spacing: 5
                    
                    Text {
                        text: "اسم المستخدم"
                        font.pixelSize: fontSizeNormal
                        color: parent.parent.parent.parent.textColor || "#2c3e50"
                    }
                    
                    TextField {
                        id: usernameField
                        Layout.fillWidth: true
                        placeholderText: "أدخل اسم المستخدم"
                        font.pixelSize: fontSizeNormal
                        selectByMouse: true
                        
                        background: Rectangle {
                            color: surfaceColor
                            border.color: dividerColor
                            border.width: 1
                            radius: 5
                        }
                        
                        Keys.onReturnPressed: passwordField.focus = true
                    }
                }
                
                // Password field
                ColumnLayout {
                    Layout.fillWidth: true
                    spacing: 5
                    
                    Text {
                        text: "كلمة المرور"
                        font.pixelSize: fontSizeNormal
                        color: parent.parent.parent.parent.textColor || "#2c3e50"
                    }
                    
                    TextField {
                        id: passwordField
                        Layout.fillWidth: true
                        placeholderText: "أدخل كلمة المرور"
                        echoMode: TextInput.Password
                        font.pixelSize: fontSizeNormal
                        selectByMouse: true
                        
                        background: Rectangle {
                            color: surfaceColor
                            border.color: dividerColor
                            border.width: 1
                            radius: 5
                        }
                        
                        Keys.onReturnPressed: loginButton.clicked()
                    }
                }
                
                // Login button
                Button {
                    id: loginButton
                    Layout.fillWidth: true
                    text: isLoading ? "جاري تسجيل الدخول..." : "تسجيل الدخول"
                    font.pixelSize: fontSizeMedium
                    enabled: !isLoading && usernameField.text.length > 0 && passwordField.text.length > 0
                    
                    background: Rectangle {
                        color: loginButton.enabled ? "#3498db" : "#bdc3c7"
                        radius: 5
                        border.color: loginButton.enabled ? "#2980b9" : "#95a5a6"
                        border.width: 1
                    }
                    
                    contentItem: Text {
                        text: loginButton.text
                        font: loginButton.font
                        color: "white"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                    
                    onClicked: {
                        if (usernameField.text && passwordField.text) {
                            isLoading = true
                            // For now, simulate successful login
                            // TODO: Implement actual login when UserViewModel is ready
                            if (usernameField.text === "admin" && passwordField.text === "admin123") {
                                mainWindow.isLoggedIn = true
                                mainWindow.currentUser = usernameField.text
                                mainWindow.showMessage("نجاح", "تم تسجيل الدخول بنجاح", "success")
                            } else {
                                mainWindow.showMessage("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة", "error")
                            }
                            isLoading = false
                        }
                    }
                }
                
                // Help text
                Text {
                    text: "المستخدم الافتراضي: admin | كلمة المرور: admin123"
                    font.pixelSize: fontSizeSmall
                    color: secondaryTextColor
                    Layout.alignment: Qt.AlignHCenter
                    wrapMode: Text.WordWrap
                }
            }
        }
    }
    
    // Reset loading state when login fails (commented out until signals are implemented)
    /*
    Connections {
        target: userViewModel
        function onLoginFailed() {
            isLoading = false
        }
        function onLoginSuccess() {
            isLoading = false
        }
    }
    */
}
