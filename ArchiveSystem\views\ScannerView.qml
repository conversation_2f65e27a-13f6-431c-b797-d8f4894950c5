import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: scannerView
    color: "#f8f9fa"
    
    property bool isScanning: false
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20
        
        // Header
        RowLayout {
            Layout.fillWidth: true
            
            Text {
                text: "📷 المسح الضوئي"
                font.pixelSize: 24
                font.bold: true
                color: "#2c3e50"
            }
            
            Item {
                Layout.fillWidth: true
            }
            
            Button {
                text: "رجوع"
                
                background: Rectangle {
                    color: "#95a5a6"
                    radius: 5
                }
                
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                
                onClicked: mainWindow.showMainView()
            }
        }
        
        // Scanner controls
        Rectangle {
            Layout.fillWidth: true
            height: 150
            color: "white"
            radius: 10
            border.color: "#e0e0e0"
            border.width: 1
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 15
                
                Text {
                    text: "إعدادات المسح"
                    font.pixelSize: 18
                    font.bold: true
                    color: "#2c3e50"
                }
                
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 20
                    
                    ColumnLayout {
                        spacing: 5
                        
                        Text {
                            text: "جودة المسح"
                            color: "#2c3e50"
                        }
                        
                        ComboBox {
                            id: qualityCombo
                            model: ["عالية (300 DPI)", "متوسطة (150 DPI)", "منخفضة (75 DPI)"]
                            currentIndex: 0
                            
                            background: Rectangle {
                                color: "white"
                                border.color: "#bdc3c7"
                                border.width: 1
                                radius: 5
                            }
                        }
                    }
                    
                    ColumnLayout {
                        spacing: 5
                        
                        Text {
                            text: "نوع المسح"
                            color: "#2c3e50"
                        }
                        
                        ComboBox {
                            id: typeCombo
                            model: ["ملون", "رمادي", "أبيض وأسود"]
                            currentIndex: 0
                            
                            background: Rectangle {
                                color: "white"
                                border.color: "#bdc3c7"
                                border.width: 1
                                radius: 5
                            }
                        }
                    }
                }
                
                Button {
                    text: isScanning ? "جاري المسح..." : "بدء المسح"
                    Layout.fillWidth: true
                    enabled: !isScanning
                    
                    background: Rectangle {
                        color: isScanning ? "#95a5a6" : "#27ae60"
                        radius: 5
                    }
                    
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                    
                    onClicked: startScan()
                }
            }
        }
        
        // Preview area
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "white"
            radius: 10
            border.color: "#e0e0e0"
            border.width: 1
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 15
                
                Text {
                    text: "معاينة المستند"
                    font.pixelSize: 18
                    font.bold: true
                    color: "#2c3e50"
                }
                
                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: "#f8f9fa"
                    border.color: "#e0e0e0"
                    border.width: 2
                    border.style: "dashed"
                    radius: 5
                    
                    ColumnLayout {
                        anchors.centerIn: parent
                        spacing: 20
                        
                        Text {
                            text: "📄"
                            font.pixelSize: 64
                            Layout.alignment: Qt.AlignHCenter
                        }
                        
                        Text {
                            id: previewText
                            text: "لا يوجد مستند ممسوح"
                            font.pixelSize: 16
                            color: "#7f8c8d"
                            Layout.alignment: Qt.AlignHCenter
                        }
                        
                        Text {
                            text: "اضغط على 'بدء المسح' لمسح مستند جديد"
                            font.pixelSize: 12
                            color: "#95a5a6"
                            Layout.alignment: Qt.AlignHCenter
                        }
                    }
                }
                
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 10
                    
                    Button {
                        text: "حفظ"
                        Layout.fillWidth: true
                        enabled: false
                        
                        background: Rectangle {
                            color: parent.enabled ? "#3498db" : "#bdc3c7"
                            radius: 5
                        }
                        
                        contentItem: Text {
                            text: parent.text
                            color: "white"
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                        }
                        
                        onClicked: saveDocument()
                    }
                    
                    Button {
                        text: "إعادة مسح"
                        Layout.fillWidth: true
                        enabled: false
                        
                        background: Rectangle {
                            color: parent.enabled ? "#f39c12" : "#bdc3c7"
                            radius: 5
                        }
                        
                        contentItem: Text {
                            text: parent.text
                            color: "white"
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                        }
                        
                        onClicked: startScan()
                    }
                    
                    Button {
                        text: "مسح"
                        Layout.fillWidth: true
                        enabled: false
                        
                        background: Rectangle {
                            color: parent.enabled ? "#e74c3c" : "#bdc3c7"
                            radius: 5
                        }
                        
                        contentItem: Text {
                            text: parent.text
                            color: "white"
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                        }
                        
                        onClicked: clearPreview()
                    }
                }
            }
        }
    }
    
    function startScan() {
        isScanning = true
        previewText.text = "جاري المسح..."
        
        // Simulate scanning process
        scanTimer.start()
    }
    
    function saveDocument() {
        mainWindow.showMessage("نجاح", "تم حفظ المستند الممسوح بنجاح", "success")
        clearPreview()
    }
    
    function clearPreview() {
        previewText.text = "لا يوجد مستند ممسوح"
        // Reset button states
        var buttons = parent.children
        for (var i = 0; i < buttons.length; i++) {
            if (buttons[i].text === "حفظ" || buttons[i].text === "إعادة مسح" || buttons[i].text === "مسح") {
                buttons[i].enabled = false
            }
        }
    }
    
    Timer {
        id: scanTimer
        interval: 3000
        repeat: false
        onTriggered: {
            isScanning = false
            previewText.text = "تم المسح بنجاح!"
            
            // Enable action buttons
            var buttons = parent.children
            for (var i = 0; i < buttons.length; i++) {
                if (buttons[i].text === "حفظ" || buttons[i].text === "إعادة مسح" || buttons[i].text === "مسح") {
                    buttons[i].enabled = true
                }
            }
            
            mainWindow.showMessage("نجاح", "تم مسح المستند بنجاح", "success")
        }
    }
}
