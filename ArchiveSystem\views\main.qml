import QtQuick 2.15
import QtQuick.Window 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
// import QtQuick.Dialogs 1.3 // Removed for PySide6 compatibility
import Qt.labs.platform 1.1 as Platform

ApplicationWindow {
    id: mainWindow
    visible: true
    width: 1280
    height: 800
    title: appController.getAppName()
    
    // RTL support
    LayoutMirroring.enabled: appController.isRtl
    LayoutMirroring.childrenInherit: true
    
    // Properties
    property bool isLoggedIn: false
    property var currentUser: ({})
    property string currentTheme: appController.theme
    property real scaleFactor: uiScale
    
    // Font sizes
    property int fontSizeSmall: 12 * scaleFactor
    property int fontSizeNormal: 14 * scaleFactor
    property int fontSizeMedium: 16 * scaleFactor
    property int fontSizeLarge: 18 * scaleFactor
    property int fontSizeHeader: 22 * scaleFactor
    
    // Colors based on theme
    property color primaryColor: currentTheme === "dark" ? "#2979FF" : "#1976D2"
    property color accentColor: currentTheme === "dark" ? "#FF4081" : "#E91E63"
    property color backgroundColor: currentTheme === "dark" ? "#121212" : "#F5F5F5"
    property color surfaceColor: currentTheme === "dark" ? "#1E1E1E" : "#FFFFFF"
    property color textColor: currentTheme === "dark" ? "#FFFFFF" : "#212121"
    property color secondaryTextColor: currentTheme === "dark" ? "#B0B0B0" : "#757575"
    property color dividerColor: currentTheme === "dark" ? "#424242" : "#BDBDBD"
    property color errorColor: "#F44336"
    property color successColor: "#4CAF50"
    property color warningColor: "#FFC107"
    
    // Fonts
    FontLoader {
        id: arabicFont
        source: "../assets/fonts/Cairo-Regular.ttf"
    }
    
    FontLoader {
        id: arabicFontBold
        source: "../assets/fonts/Cairo-Bold.ttf"
    }
    
    // Set default font
    font.family: appController.isRtl ? arabicFont.name : "Segoe UI"
    font.pixelSize: fontSizeNormal
    
    // Main content loader
    Loader {
        id: contentLoader
        anchors.fill: parent
        source: isLoggedIn ? "MainView.qml" : "LoginView.qml"
    }
    
    // Global message dialog
    MessageDialog {
        id: messageDialog
        title: "رسالة"
        icon: MessageDialog.Information
        
        function showError(message) {
            title = "خطأ"
            text = message
            icon = MessageDialog.Critical
            open()
        }
        
        function showInfo(message) {
            title = "معلومات"
            text = message
            icon = MessageDialog.Information
            open()
        }
        
        function showSuccess(message) {
            title = "نجاح"
            text = message
            icon = MessageDialog.Information
            open()
        }
        
        function showWarning(message) {
            title = "تحذير"
            text = message
            icon = MessageDialog.Warning
            open()
        }
    }
    
    // Global file dialog for opening files
    Platform.FileDialog {
        id: openFileDialog
        title: "اختر ملفًا"
        folder: Platform.StandardPaths.writableLocation(Platform.StandardPaths.DocumentsLocation)
        nameFilters: ["جميع الملفات (*.*)"]
        selectMultiple: false
        
        property var callback: null
        
        onAccepted: {
            if (callback) {
                callback(fileUrl)
            }
        }
        
        function openForCallback(cb, filters) {
            callback = cb
            if (filters) {
                nameFilters = filters
            }
            open()
        }
    }
    
    // Global file dialog for saving files
    Platform.FileDialog {
        id: saveFileDialog
        title: "حفظ الملف"
        folder: Platform.StandardPaths.writableLocation(Platform.StandardPaths.DocumentsLocation)
        selectExisting: false
        
        property var callback: null
        
        onAccepted: {
            if (callback) {
                callback(fileUrl)
            }
        }
        
        function saveForCallback(cb, defaultFileName, filters) {
            callback = cb
            if (defaultFileName) {
                selectExisting = false
                selectFolder = false
                fileMode = Platform.FileDialog.SaveFile
            }
            if (filters) {
                nameFilters = filters
            }
            open()
        }
    }
    
    // Handle login success
    Connections {
        target: userViewModel
        
        function onUserChanged() {
            var user = userViewModel.currentUser
            if (user && user.id) {
                isLoggedIn = true
                currentUser = user
                contentLoader.source = "MainView.qml"
            } else {
                isLoggedIn = false
                currentUser = {}
                contentLoader.source = "LoginView.qml"
            }
        }
        
        function onErrorOccurred(message) {
            messageDialog.showError(message)
        }
        
        function onOperationSucceeded(message) {
            messageDialog.showSuccess(message)
        }
    }
    
    // Handle document operations
    Connections {
        target: documentViewModel
        
        function onErrorOccurred(message) {
            messageDialog.showError(message)
        }
        
        function onOperationSucceeded(message) {
            messageDialog.showSuccess(message)
        }
    }
    
    // Handle category operations
    Connections {
        target: categoryViewModel
        
        function onErrorOccurred(message) {
            messageDialog.showError(message)
        }
        
        function onOperationSucceeded(message) {
            messageDialog.showSuccess(message)
        }
    }
    
    // Handle scanner operations
    Connections {
        target: scannerViewModel
        
        function onErrorOccurred(message) {
            messageDialog.showError(message)
        }
    }
    
    // Handle theme changes
    Connections {
        target: appController
        
        function onThemeChanged(theme) {
            currentTheme = theme
        }
    }
    
    // Component for creating a styled button
    Component {
        id: styledButtonComponent
        
        Button {
            id: button
            
            property color buttonColor: primaryColor
            property color textColor: "white"
            property color hoverColor: Qt.lighter(buttonColor, 1.1)
            property color pressedColor: Qt.darker(buttonColor, 1.1)
            property bool isOutlined: false
            
            contentItem: Text {
                text: button.text
                font: button.font
                color: button.isOutlined ? button.buttonColor : button.textColor
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                elide: Text.ElideRight
            }
            
            background: Rectangle {
                implicitWidth: 100
                implicitHeight: 40
                color: button.isOutlined ? "transparent" : 
                       button.pressed ? button.pressedColor : 
                       button.hovered ? button.hoverColor : 
                       button.buttonColor
                border.color: button.buttonColor
                border.width: button.isOutlined ? 1 : 0
                radius: 4
            }
        }
    }
    
    // Component for creating a styled text field
    Component {
        id: styledTextFieldComponent
        
        TextField {
            id: textField
            
            property color borderColor: activeFocus ? primaryColor : dividerColor
            property color backgroundColor: surfaceColor
            
            placeholderTextColor: secondaryTextColor
            color: textColor
            
            background: Rectangle {
                implicitWidth: 200
                implicitHeight: 40
                color: textField.backgroundColor
                border.color: textField.borderColor
                border.width: 1
                radius: 4
            }
        }
    }
    
    // Component for creating a styled combo box
    Component {
        id: styledComboBoxComponent
        
        ComboBox {
            id: comboBox
            
            property color borderColor: activeFocus ? primaryColor : dividerColor
            property color backgroundColor: surfaceColor
            
            background: Rectangle {
                implicitWidth: 200
                implicitHeight: 40
                color: comboBox.backgroundColor
                border.color: comboBox.borderColor
                border.width: 1
                radius: 4
            }
            
            contentItem: Text {
                leftPadding: 10
                rightPadding: comboBox.indicator.width + 10
                text: comboBox.displayText
                font: comboBox.font
                color: textColor
                verticalAlignment: Text.AlignVCenter
                elide: Text.ElideRight
            }
            
            delegate: ItemDelegate {
                width: comboBox.width
                contentItem: Text {
                    text: modelData.label !== undefined ? modelData.label : modelData
                    color: textColor
                    font: comboBox.font
                    elide: Text.ElideRight
                    verticalAlignment: Text.AlignVCenter
                }
                highlighted: comboBox.highlightedIndex === index
                background: Rectangle {
                    color: highlighted ? Qt.lighter(primaryColor, 1.8) : surfaceColor
                }
            }
        }
    }
    
    // Component for creating a styled check box
    Component {
        id: styledCheckBoxComponent
        
        CheckBox {
            id: checkBox
            
            contentItem: Text {
                text: checkBox.text
                font: checkBox.font
                color: textColor
                verticalAlignment: Text.AlignVCenter
                leftPadding: checkBox.indicator.width + 4
            }
            
            indicator: Rectangle {
                implicitWidth: 20
                implicitHeight: 20
                x: 0
                y: parent.height / 2 - height / 2
                radius: 3
                color: surfaceColor
                border.color: checkBox.checked ? primaryColor : dividerColor
                border.width: 1
                
                Rectangle {
                    width: 10
                    height: 10
                    x: 5
                    y: 5
                    radius: 2
                    color: primaryColor
                    visible: checkBox.checked
                }
            }
        }
    }
    
    // Component for creating a styled radio button
    Component {
        id: styledRadioButtonComponent
        
        RadioButton {
            id: radioButton
            
            contentItem: Text {
                text: radioButton.text
                font: radioButton.font
                color: textColor
                verticalAlignment: Text.AlignVCenter
                leftPadding: radioButton.indicator.width + 4
            }
            
            indicator: Rectangle {
                implicitWidth: 20
                implicitHeight: 20
                x: 0
                y: parent.height / 2 - height / 2
                radius: 10
                color: surfaceColor
                border.color: radioButton.checked ? primaryColor : dividerColor
                border.width: 1
                
                Rectangle {
                    width: 10
                    height: 10
                    x: 5
                    y: 5
                    radius: 5
                    color: primaryColor
                    visible: radioButton.checked
                }
            }
        }
    }
    
    // Component for creating a styled tab button
    Component {
        id: styledTabButtonComponent
        
        TabButton {
            id: tabButton
            
            contentItem: Text {
                text: tabButton.text
                font: tabButton.font
                color: tabButton.checked ? primaryColor : secondaryTextColor
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                elide: Text.ElideRight
            }
            
            background: Rectangle {
                implicitWidth: 100
                implicitHeight: 40
                color: "transparent"
                border.color: tabButton.checked ? primaryColor : "transparent"
                border.width: tabButton.checked ? 2 : 0
                radius: 0
            }
        }
    }
    
    // Component for creating a card container
    Component {
        id: cardComponent
        
        Rectangle {
            id: card
            color: surfaceColor
            radius: 4
            border.color: dividerColor
            border.width: 1
            
            property alias title: titleText.text
            property bool showHeader: title !== ""
            
            ColumnLayout {
                anchors.fill: parent
                spacing: 0
                
                Rectangle {
                    Layout.fillWidth: true
                    height: 40
                    color: Qt.lighter(primaryColor, 1.8)
                    visible: card.showHeader
                    radius: 4
                    
                    Rectangle {
                        width: parent.width
                        height: parent.height / 2
                        anchors.bottom: parent.bottom
                        color: parent.color
                    }
                    
                    Text {
                        id: titleText
                        anchors.fill: parent
                        anchors.margins: 10
                        font.pixelSize: fontSizeMedium
                        font.bold: true
                        color: textColor
                        elide: Text.ElideRight
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                
                Item {
                    id: contentItem
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.margins: 10
                }
            }
            
            default property alias content: contentItem.data
        }
    }
    
    // Component for creating a list item
    Component {
        id: listItemComponent
        
        Rectangle {
            id: listItem
            width: parent.width
            height: 50
            color: mouseArea.containsMouse ? Qt.lighter(primaryColor, 1.9) : "transparent"
            
            property alias text: itemText.text
            property alias icon: itemIcon.text
            property alias iconColor: itemIcon.color
            property bool selected: false
            signal clicked()
            
            Rectangle {
                width: 4
                height: parent.height
                color: listItem.selected ? primaryColor : "transparent"
            }
            
            Row {
                anchors.fill: parent
                anchors.margins: 10
                spacing: 10
                
                Text {
                    id: itemIcon
                    font.family: "FontAwesome"
                    font.pixelSize: fontSizeMedium
                    color: listItem.selected ? primaryColor : secondaryTextColor
                    anchors.verticalCenter: parent.verticalCenter
                }
                
                Text {
                    id: itemText
                    font.pixelSize: fontSizeNormal
                    color: listItem.selected ? primaryColor : textColor
                    anchors.verticalCenter: parent.verticalCenter
                    width: parent.width - itemIcon.width - 10
                    elide: Text.ElideRight
                }
            }
            
            MouseArea {
                id: mouseArea
                anchors.fill: parent
                hoverEnabled: true
                onClicked: listItem.clicked()
            }
        }
    }
    
    // Component for creating a section header
    Component {
        id: sectionHeaderComponent
        
        Rectangle {
            id: sectionHeader
            width: parent.width
            height: 30
            color: Qt.lighter(primaryColor, 1.9)
            
            property alias text: headerText.text
            
            Text {
                id: headerText
                anchors.fill: parent
                anchors.margins: 5
                font.pixelSize: fontSizeNormal
                font.bold: true
                color: primaryColor
                verticalAlignment: Text.AlignVCenter
            }
        }
    }
    
    // Component for creating a document item
    Component {
        id: documentItemComponent
        
        Rectangle {
            id: documentItem
            width: parent.width
            height: 80
            color: mouseArea.containsMouse ? Qt.lighter(primaryColor, 1.9) : surfaceColor
            border.color: dividerColor
            border.width: 1
            radius: 4
            
            property string title: ""
            property string description: ""
            property string category: ""
            property string date: ""
            property string fileType: ""
            property int documentId: 0
            signal clicked()
            signal menuRequested(var mouse)
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 10
                spacing: 10
                
                Rectangle {
                    Layout.preferredWidth: 60
                    Layout.preferredHeight: 60
                    color: Qt.lighter(primaryColor, 1.7)
                    radius: 4
                    
                    Text {
                        anchors.centerIn: parent
                        text: getFileTypeIcon(documentItem.fileType)
                        font.family: "FontAwesome"
                        font.pixelSize: 24
                        color: primaryColor
                    }
                }
                
                ColumnLayout {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    spacing: 2
                    
                    Text {
                        text: documentItem.title
                        font.pixelSize: fontSizeMedium
                        font.bold: true
                        color: textColor
                        elide: Text.ElideRight
                        Layout.fillWidth: true
                    }
                    
                    Text {
                        text: documentItem.description
                        font.pixelSize: fontSizeSmall
                        color: secondaryTextColor
                        elide: Text.ElideRight
                        Layout.fillWidth: true
                        visible: text !== ""
                    }
                    
                    RowLayout {
                        Layout.fillWidth: true
                        spacing: 10
                        
                        Text {
                            text: documentItem.category
                            font.pixelSize: fontSizeSmall
                            color: secondaryTextColor
                        }
                        
                        Text {
                            text: documentItem.date
                            font.pixelSize: fontSizeSmall
                            color: secondaryTextColor
                        }
                    }
                }
                
                Item {
                    Layout.preferredWidth: 30
                    Layout.fillHeight: true
                    
                    Text {
                        anchors.centerIn: parent
                        text: "\uf142" // FontAwesome vertical ellipsis
                        font.family: "FontAwesome"
                        font.pixelSize: fontSizeMedium
                        color: secondaryTextColor
                        
                        MouseArea {
                            anchors.fill: parent
                            anchors.margins: -10
                            onClicked: documentItem.menuRequested(mouse)
                        }
                    }
                }
            }
            
            MouseArea {
                id: mouseArea
                anchors.fill: parent
                hoverEnabled: true
                onClicked: documentItem.clicked()
                acceptedButtons: Qt.LeftButton
            }
            
            function getFileTypeIcon(type) {
                switch(type.toLowerCase()) {
                    case "pdf": return "\uf1c1";
                    case "doc":
                    case "docx": return "\uf1c2";
                    case "xls":
                    case "xlsx": return "\uf1c3";
                    case "ppt":
                    case "pptx": return "\uf1c4";
                    case "jpg":
                    case "jpeg":
                    case "png":
                    case "gif":
                    case "bmp": return "\uf1c5";
                    case "zip":
                    case "rar": return "\uf1c6";
                    case "txt": return "\uf0f6";
                    default: return "\uf15b";
                }
            }
        }
    }
    
    // Component for creating a user item
    Component {
        id: userItemComponent
        
        Rectangle {
            id: userItem
            width: parent.width
            height: 60
            color: mouseArea.containsMouse ? Qt.lighter(primaryColor, 1.9) : surfaceColor
            border.color: dividerColor
            border.width: 1
            radius: 4
            
            property string username: ""
            property string fullName: ""
            property string role: ""
            property bool isActive: true
            property int userId: 0
            signal clicked()
            signal menuRequested(var mouse)
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 10
                spacing: 10
                
                Rectangle {
                    Layout.preferredWidth: 40
                    Layout.preferredHeight: 40
                    color: userItem.isActive ? Qt.lighter(primaryColor, 1.7) : Qt.lighter(dividerColor, 1.2)
                    radius: 20
                    
                    Text {
                        anchors.centerIn: parent
                        text: userItem.username.charAt(0).toUpperCase()
                        font.pixelSize: 18
                        font.bold: true
                        color: userItem.isActive ? primaryColor : secondaryTextColor
                    }
                }
                
                ColumnLayout {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    spacing: 2
                    
                    Text {
                        text: userItem.fullName
                        font.pixelSize: fontSizeMedium
                        font.bold: true
                        color: textColor
                        elide: Text.ElideRight
                        Layout.fillWidth: true
                    }
                    
                    Text {
                        text: userItem.username + " - " + userItem.role
                        font.pixelSize: fontSizeSmall
                        color: secondaryTextColor
                        elide: Text.ElideRight
                        Layout.fillWidth: true
                    }
                }
                
                Rectangle {
                    Layout.preferredWidth: 10
                    Layout.preferredHeight: 10
                    radius: 5
                    color: userItem.isActive ? successColor : errorColor
                }
                
                Item {
                    Layout.preferredWidth: 30
                    Layout.fillHeight: true
                    
                    Text {
                        anchors.centerIn: parent
                        text: "\uf142" // FontAwesome vertical ellipsis
                        font.family: "FontAwesome"
                        font.pixelSize: fontSizeMedium
                        color: secondaryTextColor
                        
                        MouseArea {
                            anchors.fill: parent
                            anchors.margins: -10
                            onClicked: userItem.menuRequested(mouse)
                        }
                    }
                }
            }
            
            MouseArea {
                id: mouseArea
                anchors.fill: parent
                hoverEnabled: true
                onClicked: userItem.clicked()
                acceptedButtons: Qt.LeftButton
            }
        }
    }
    
    // Component for creating a category item
    Component {
        id: categoryItemComponent
        
        Rectangle {
            id: categoryItem
            width: parent.width
            height: 50
            color: mouseArea.containsMouse ? Qt.lighter(primaryColor, 1.9) : "transparent"
            
            property string name: ""
            property string description: ""
            property int documentCount: 0
            property bool hasChildren: false
            property bool isExpanded: false
            property int categoryId: 0
            property int level: 0
            signal clicked()
            signal toggleExpand()
            signal menuRequested(var mouse)
            
            RowLayout {
                anchors.fill: parent
                anchors.leftMargin: 10 + (categoryItem.level * 20)
                anchors.rightMargin: 10
                anchors.topMargin: 5
                anchors.bottomMargin: 5
                spacing: 10
                
                Text {
                    text: categoryItem.hasChildren ? (categoryItem.isExpanded ? "\uf107" : "\uf105") : "\uf0da"
                    font.family: "FontAwesome"
                    font.pixelSize: fontSizeNormal
                    color: categoryItem.hasChildren ? primaryColor : "transparent"
                    
                    MouseArea {
                        anchors.fill: parent
                        anchors.margins: -5
                        onClicked: {
                            if (categoryItem.hasChildren) {
                                categoryItem.toggleExpand()
                            }
                        }
                    }
                }
                
                Text {
                    text: "\uf07b" // FontAwesome folder
                    font.family: "FontAwesome"
                    font.pixelSize: fontSizeNormal
                    color: primaryColor
                }
                
                Text {
                    text: categoryItem.name
                    font.pixelSize: fontSizeNormal
                    color: textColor
                    elide: Text.ElideRight
                    Layout.fillWidth: true
                }
                
                Text {
                    text: categoryItem.documentCount.toString()
                    font.pixelSize: fontSizeSmall
                    color: secondaryTextColor
                }
                
                Item {
                    Layout.preferredWidth: 30
                    Layout.fillHeight: true
                    
                    Text {
                        anchors.centerIn: parent
                        text: "\uf142" // FontAwesome vertical ellipsis
                        font.family: "FontAwesome"
                        font.pixelSize: fontSizeNormal
                        color: secondaryTextColor
                        
                        MouseArea {
                            anchors.fill: parent
                            anchors.margins: -10
                            onClicked: categoryItem.menuRequested(mouse)
                        }
                    }
                }
            }
            
            MouseArea {
                id: mouseArea
                anchors.fill: parent
                hoverEnabled: true
                onClicked: categoryItem.clicked()
                acceptedButtons: Qt.LeftButton
            }
        }
    }
    
    // Component for creating a breadcrumb navigation
    Component {
        id: breadcrumbComponent
        
        RowLayout {
            id: breadcrumb
            spacing: 5
            
            property var items: []
            signal itemClicked(int index, var item)
            
            Repeater {
                model: breadcrumb.items
                
                RowLayout {
                    spacing: 5
                    
                    Text {
                        text: "\uf054" // FontAwesome chevron-right
                        font.family: "FontAwesome"
                        font.pixelSize: fontSizeSmall
                        color: secondaryTextColor
                        visible: index > 0
                    }
                    
                    Text {
                        text: modelData.name
                        font.pixelSize: fontSizeNormal
                        color: index === breadcrumb.items.length - 1 ? textColor : primaryColor
                        
                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                if (index < breadcrumb.items.length - 1) {
                                    breadcrumb.itemClicked(index, modelData)
                                }
                            }
                            cursorShape: index < breadcrumb.items.length - 1 ? Qt.PointingHandCursor : Qt.ArrowCursor
                        }
                    }
                }
            }
        }
    }
    
    // Component for creating a search box
    Component {
        id: searchBoxComponent
        
        Rectangle {
            id: searchBox
            height: 40
            radius: 20
            color: surfaceColor
            border.color: searchField.activeFocus ? primaryColor : dividerColor
            border.width: 1
            
            property alias text: searchField.text
            property alias placeholder: searchField.placeholderText
            signal search(string text)
            signal textChanged(string text)
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 5
                spacing: 5
                
                Text {
                    text: "\uf002" // FontAwesome search
                    font.family: "FontAwesome"
                    font.pixelSize: fontSizeNormal
                    color: secondaryTextColor
                    Layout.leftMargin: 10
                }
                
                TextField {
                    id: searchField
                    Layout.fillWidth: true
                    placeholderText: "بحث..."
                    background: null
                    color: textColor
                    selectByMouse: true
                    
                    onAccepted: searchBox.search(text)
                    onTextChanged: searchBox.textChanged(text)
                }
                
                Text {
                    text: "\uf00d" // FontAwesome times
                    font.family: "FontAwesome"
                    font.pixelSize: fontSizeNormal
                    color: searchField.text ? secondaryTextColor : "transparent"
                    visible: searchField.text !== ""
                    Layout.rightMargin: 10
                    
                    MouseArea {
                        anchors.fill: parent
                        anchors.margins: -5
                        onClicked: {
                            searchField.text = ""
                            searchBox.search("")
                        }
                    }
                }
            }
        }
    }
    
    // Component for creating a date picker
    Component {
        id: datePickerComponent
        
        TextField {
            id: dateField
            readOnly: true
            
            property date selectedDate: new Date()
            property bool showClearButton: true
            
            text: formatDate(selectedDate)
            placeholderText: "اختر تاريخًا..."
            
            MouseArea {
                anchors.fill: parent
                onClicked: dateDialog.open()
            }
            
            Button {
                anchors.right: parent.right
                anchors.rightMargin: 5
                anchors.verticalCenter: parent.verticalCenter
                width: 20
                height: 20
                visible: dateField.showClearButton && dateField.text !== ""
                
                contentItem: Text {
                    text: "\uf00d" // FontAwesome times
                    font.family: "FontAwesome"
                    font.pixelSize: fontSizeSmall
                    color: secondaryTextColor
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                
                background: Rectangle {
                    color: "transparent"
                }
                
                onClicked: {
                    dateField.selectedDate = new Date(0) // Invalid date
                    dateField.text = ""
                }
            }
            
            Dialog {
                id: dateDialog
                title: "اختر تاريخًا"
                standardButtons: Dialog.Ok | Dialog.Cancel
                
                Calendar {
                    id: calendar
                    selectedDate: dateField.selectedDate.getTime() > 0 ? dateField.selectedDate : new Date()
                }
                
                onAccepted: {
                    dateField.selectedDate = calendar.selectedDate
                    dateField.text = formatDate(calendar.selectedDate)
                }
            }
            
            function formatDate(date) {
                if (date.getTime() <= 0) return ""
                return Qt.formatDate(date, "yyyy-MM-dd")
            }
        }
    }
    
    // Component for creating a file upload area
    Component {
        id: fileUploadComponent
        
        Rectangle {
            id: uploadArea
            color: dropArea.containsDrag ? Qt.lighter(primaryColor, 1.8) : Qt.lighter(backgroundColor, 1.1)
            border.color: dropArea.containsDrag ? primaryColor : dividerColor
            border.width: 1
            radius: 4
            
            property string selectedFilePath: ""
            property string selectedFileName: ""
            property var selectedFileData: null
            signal fileSelected(string filePath, string fileName, var fileData)
            
            DropArea {
                id: dropArea
                anchors.fill: parent
                
                onDropped: {
                    if (drop.hasUrls) {
                        var filePath = drop.urls[0].toString()
                        // Remove 'file:///' prefix
                        filePath = filePath.replace(/^(file:\/{3})/, "")
                        var fileName = filePath.split('/').pop()
                        uploadArea.selectedFilePath = filePath
                        uploadArea.selectedFileName = fileName
                        uploadArea.fileSelected(filePath, fileName, null)
                    }
                }
            }
            
            ColumnLayout {
                anchors.centerIn: parent
                spacing: 10
                
                Text {
                    text: "\uf0ee" // FontAwesome cloud-upload
                    font.family: "FontAwesome"
                    font.pixelSize: 32
                    color: primaryColor
                    Layout.alignment: Qt.AlignHCenter
                }
                
                Text {
                    text: uploadArea.selectedFileName || "اسحب الملفات هنا أو انقر للتصفح"
                    color: textColor
                    font.pixelSize: fontSizeNormal
                    horizontalAlignment: Text.AlignHCenter
                    Layout.alignment: Qt.AlignHCenter
                }
                
                Button {
                    text: "تصفح الملفات"
                    Layout.alignment: Qt.AlignHCenter
                    
                    onClicked: {
                        openFileDialog.openForCallback(function(fileUrl) {
                            var filePath = fileUrl.toString()
                            // Remove 'file:///' prefix
                            filePath = filePath.replace(/^(file:\/{3})/, "")
                            var fileName = filePath.split('/').pop()
                            uploadArea.selectedFilePath = filePath
                            uploadArea.selectedFileName = fileName
                            uploadArea.fileSelected(filePath, fileName, null)
                        })
                    }
                }
            }
        }
    }
    
    // Component for creating a pagination control
    Component {
        id: paginationComponent
        
        Row {
            id: pagination
            spacing: 5
            
            property int currentPage: 1
            property int totalPages: 1
            signal pageChanged(int page)
            
            Button {
                text: "\uf100" // FontAwesome angle-double-left
                font.family: "FontAwesome"
                enabled: pagination.currentPage > 1
                
                onClicked: {
                    pagination.currentPage = 1
                    pagination.pageChanged(pagination.currentPage)
                }
            }
            
            Button {
                text: "\uf104" // FontAwesome angle-left
                font.family: "FontAwesome"
                enabled: pagination.currentPage > 1
                
                onClicked: {
                    pagination.currentPage--
                    pagination.pageChanged(pagination.currentPage)
                }
            }
            
            Repeater {
                model: getPaginationModel()
                
                Button {
                    text: modelData === -1 ? "..." : modelData
                    enabled: modelData !== -1
                    highlighted: modelData === pagination.currentPage
                    
                    onClicked: {
                        if (modelData !== -1) {
                            pagination.currentPage = modelData
                            pagination.pageChanged(pagination.currentPage)
                        }
                    }
                }
            }
            
            Button {
                text: "\uf105" // FontAwesome angle-right
                font.family: "FontAwesome"
                enabled: pagination.currentPage < pagination.totalPages
                
                onClicked: {
                    pagination.currentPage++
                    pagination.pageChanged(pagination.currentPage)
                }
            }
            
            Button {
                text: "\uf101" // FontAwesome angle-double-right
                font.family: "FontAwesome"
                enabled: pagination.currentPage < pagination.totalPages
                
                onClicked: {
                    pagination.currentPage = pagination.totalPages
                    pagination.pageChanged(pagination.currentPage)
                }
            }
            
            function getPaginationModel() {
                var result = []
                var totalPages = pagination.totalPages
                var currentPage = pagination.currentPage
                
                if (totalPages <= 7) {
                    // Show all pages
                    for (var i = 1; i <= totalPages; i++) {
                        result.push(i)
                    }
                } else {
                    // Always show first and last page
                    result.push(1)
                    
                    if (currentPage <= 3) {
                        // Show first 5 pages, then ellipsis, then last page
                        result.push(2, 3, 4, 5, -1, totalPages)
                    } else if (currentPage >= totalPages - 2) {
                        // Show first page, then ellipsis, then last 5 pages
                        result.push(-1, totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages)
                    } else {
                        // Show first page, then ellipsis, then current page and neighbors, then ellipsis, then last page
                        result.push(-1, currentPage - 1, currentPage, currentPage + 1, -1, totalPages)
                    }
                }
                
                return result
            }
        }
    }
}